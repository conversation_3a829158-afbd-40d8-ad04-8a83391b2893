#!/bin/bash

# Flutter Scaffold Pre-commit Quality Check
# 在git commit前自动执行代码质量检查

set -e

echo "🔍 Flutter Scaffold Pre-commit Quality Check"
echo "============================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查是否在Flutter项目根目录
if [ ! -f "pubspec.yaml" ]; then
    echo -e "${RED}❌ Error: Not in Flutter project root directory${NC}"
    exit 1
fi

# 1. 代码格式化检查
echo -e "${BLUE}1. 检查代码格式...${NC}"
if ! dart format --set-exit-if-changed lib/ test/ 2>/dev/null; then
    echo -e "${YELLOW}⚠️  Code formatting issues found. Auto-fixing...${NC}"
    dart format lib/ test/
    echo -e "${GREEN}✅ Code formatted successfully${NC}"
else
    echo -e "${GREEN}✅ Code formatting is correct${NC}"
fi

# 2. 静态分析检查
echo -e "${BLUE}2. 运行静态分析...${NC}"
if flutter analyze --no-fatal-infos; then
    echo -e "${GREEN}✅ Static analysis passed${NC}"
else
    echo -e "${RED}❌ Static analysis failed${NC}"
    echo -e "${YELLOW}💡 Fix the analysis issues before committing${NC}"
    exit 1
fi

# 3. 依赖检查
echo -e "${BLUE}3. 检查依赖状态...${NC}"
if flutter pub deps --no-dev 2>/dev/null | grep -q "No dependencies"; then
    echo -e "${YELLOW}⚠️  No dependencies found${NC}"
else
    echo -e "${GREEN}✅ Dependencies are properly configured${NC}"
fi

# 4. 测试运行（快速测试）
echo -e "${BLUE}4. 运行快速测试...${NC}"
if flutter test --no-coverage --reporter=compact 2>/dev/null; then
    echo -e "${GREEN}✅ All tests passed${NC}"
else
    echo -e "${YELLOW}⚠️  Some tests failed or no tests found${NC}"
    echo -e "${YELLOW}💡 Consider adding tests for your changes${NC}"
fi

# 5. 构建检查（仅检查是否能编译）
echo -e "${BLUE}5. 检查构建状态...${NC}"
if flutter build apk --debug --no-shrink 2>/dev/null >/dev/null; then
    echo -e "${GREEN}✅ Build check passed${NC}"
else
    echo -e "${RED}❌ Build check failed${NC}"
    echo -e "${YELLOW}💡 Fix build errors before committing${NC}"
    exit 1
fi

# 6. 提交信息检查
echo -e "${BLUE}6. 检查提交信息格式...${NC}"
COMMIT_MSG_FILE="$1"
if [ -n "$COMMIT_MSG_FILE" ] && [ -f "$COMMIT_MSG_FILE" ]; then
    COMMIT_MSG=$(cat "$COMMIT_MSG_FILE")
    
    # 检查提交信息格式 (Conventional Commits)
    if echo "$COMMIT_MSG" | grep -qE "^(feat|fix|docs|style|refactor|test|chore|perf|ci|build)(\(.+\))?: .+"; then
        echo -e "${GREEN}✅ Commit message format is correct${NC}"
    else
        echo -e "${YELLOW}⚠️  Commit message doesn't follow conventional format${NC}"
        echo -e "${YELLOW}💡 Use format: type(scope): description${NC}"
        echo -e "${YELLOW}   Examples: feat(auth): add login functionality${NC}"
        echo -e "${YELLOW}            fix(ui): resolve button alignment issue${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  No commit message file provided${NC}"
fi

# 7. 安全检查
echo -e "${BLUE}7. 基础安全检查...${NC}"
SECURITY_ISSUES=0

# 检查是否有硬编码的敏感信息
if grep -r "password\|secret\|key\|token" lib/ --include="*.dart" | grep -v "// TODO\|//" | head -5; then
    echo -e "${YELLOW}⚠️  Potential hardcoded secrets found${NC}"
    SECURITY_ISSUES=$((SECURITY_ISSUES + 1))
fi

# 检查是否有调试代码
if grep -r "print\|debugPrint" lib/ --include="*.dart" | grep -v "appLogger\|// TODO" | head -3; then
    echo -e "${YELLOW}⚠️  Debug print statements found${NC}"
    echo -e "${YELLOW}💡 Consider using appLogger instead of print${NC}"
    SECURITY_ISSUES=$((SECURITY_ISSUES + 1))
fi

if [ $SECURITY_ISSUES -eq 0 ]; then
    echo -e "${GREEN}✅ Basic security check passed${NC}"
fi

# 8. 性能检查
echo -e "${BLUE}8. 基础性能检查...${NC}"
PERF_ISSUES=0

# 检查是否有性能问题的模式
if grep -r "setState.*build\|build.*setState" lib/ --include="*.dart" | head -3; then
    echo -e "${YELLOW}⚠️  Potential setState in build method found${NC}"
    PERF_ISSUES=$((PERF_ISSUES + 1))
fi

if [ $PERF_ISSUES -eq 0 ]; then
    echo -e "${GREEN}✅ Basic performance check passed${NC}"
fi

echo ""
echo -e "${GREEN}🎉 Pre-commit checks completed successfully!${NC}"
echo -e "${BLUE}📝 Summary:${NC}"
echo -e "   ✅ Code formatting: OK"
echo -e "   ✅ Static analysis: OK"
echo -e "   ✅ Dependencies: OK"
echo -e "   ✅ Tests: OK"
echo -e "   ✅ Build: OK"
if [ $SECURITY_ISSUES -gt 0 ]; then
    echo -e "   ⚠️  Security: $SECURITY_ISSUES warnings"
fi
if [ $PERF_ISSUES -gt 0 ]; then
    echo -e "   ⚠️  Performance: $PERF_ISSUES warnings"
fi

echo ""
echo -e "${GREEN}✅ Ready to commit!${NC}"

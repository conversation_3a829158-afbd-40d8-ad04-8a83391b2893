# Mason Bricks 使用指南 (精简版)

## 🎯 什么是Bricks？

Bricks是**Mason代码模板**，用于**标准化代码生成**，确保所有功能模块遵循统一的Clean Architecture结构。

## 📦 核心模板清单 (精简至5个)

| 模板名称 | 命令示例 | 生成内容 | 使用场景 |
|---------|----------|----------|----------|
| **feature** | `mason make feature` | 完整功能模块 | 新建业务功能 (包含所有层级 + API客户端) |
| **page** | `mason make page` | 页面组件 | 新建页面 (主题集成 + BLoC支持) |
| **widget** | `mason make widget` | UI组件 | 创建可复用UI组件 |
| **validator** | `mason make validator` | 验证器 | 输入验证逻辑 (业务规则 + 国际化) |
| **adr** | `mason make adr` | 架构决策记录 | 记录技术决策 |

## 🗑️ 已移除的冗余模板

以下模板已被移除，因为它们的功能已被 `feature` 模板完全包含：
- ~~model~~ → 使用 `feature` 模板
- ~~repository~~ → 使用 `feature` 模板
- ~~usecase~~ → 使用 `feature` 模板
- ~~bloc~~ → 使用 `feature` 模板
- ~~data_source~~ → 使用 `feature` 模板
- ~~api_client~~ → 使用 `feature` 模板（RemoteDataSource）
- ~~test~~ → 使用 `feature` 模板
- ~~service~~ → 根据需求使用 `feature` 或手动创建
- ~~feature_complete~~ → 功能不完整，已删除

## 🚀 快速开始

### 1. 安装Mason
```bash
dart pub global activate mason_cli
```

### 2. 初始化
```bash
mason init
```

### 3. 使用模板

#### 🔥 创建完整功能
```bash
# 生成认证功能完整模块 (包含所有层级 + API客户端)
mason make feature --name auth
# 生成：RemoteDataSource (API客户端)、Repository、UseCase、BLoC、Page等

# 生成商品管理功能 (包含完整的API集成)
mason make feature --name product
# 生成：ProductRemoteDataSource 作为API客户端，处理所有产品相关的API调用
```

#### 🎨 创建页面
```bash
# 生成登录页面 (带BLoC支持)
mason make page --name login --feature auth --bloc true

# 生成设置页面 (无BLoC)
mason make page --name settings --feature core --bloc false
```



#### ✅ 创建验证器
```bash
# 生成用户输入验证器
mason make validator --name user_input --feature auth

# 为widget创建组件测试
mason make test --name login_page --type widget --feature auth
```

#### ⚙️ 创建服务组件
```bash
# 创建后台服务
mason make service --name notification --type background --feature core

# 创建通知服务
mason make service --name push --type notification --feature core
```

## 📁 生成文件结构

### Feature模板输出
```
lib/features/[name]/
├── data/
│   ├── datasources/
│   ├── models/
│   └── repositories/
├── domain/
│   ├── entities/
│   ├── repositories/
│   ├── usecases/
│   └── validators/
├── presentation/
│   ├── bloc/
│   ├── pages/
│   └── widgets/
└── test/
    ├── unit/
    ├── widget/
    └── integration/
```

### Service模板输出
```
lib/features/[feature]/domain/services/
└── [name]_service.dart
```

### Test模板输出
```
lib/features/[feature]/test/[type]/
└── [name]_test.dart
```

## 🎯 实际工作流

### 场景1：新建用户管理功能
```bash
# 1. 创建完整功能
mason make feature --name user_management

# 2. 添加用户详情页面
mason make page --name user_detail --feature user_management

# 3. 添加用户设置页面
mason make page --name user_settings --feature user_management

# 4. 为功能添加测试
mason make test --name user_management --type unit --feature user_management
```

### 场景2：快速原型开发
```bash
# 先创建简单页面验证想法
mason make page --name prototype

# 确认需求后扩展为完整功能
mason make feature --name confirmed_feature
```

### 场景3：添加网络功能
```bash
# 1. 创建API客户端
mason make api_client --name user --base_url https://api.example.com

# 2. 创建数据源
mason make data_source --name user --type remote --feature user_management

# 3. 添加验证器
mason make validator --name user --feature user_management
```

## 🔧 高级用法

### 自定义变量
所有模板支持智能替换：
- `{{name}}` → 保持原样
- `{{name.pascalCase}}` → UserProfile
- `{{name.snakeCase}}` → user_profile
- `{{name.camelCase}}` → userProfile

### 批量生成
```bash
# 创建完整功能模块 (一次性生成所有需要的文件)
mason make feature --name order

# 为现有功能添加额外页面
mason make page --name order_list --feature order --bloc true
mason make page --name order_detail --feature order --bloc true

# 如需外部服务集成，手动创建或扩展 feature 模板生成的 RemoteDataSource
```

## 🌐 API 客户端集成说明

### **feature 模板包含完整的 API 客户端功能**

每个 `feature` 模板都会生成一个 `RemoteDataSource`，它就是该功能的 API 客户端：

```dart
// 生成的 RemoteDataSource 示例
@lazySingleton
@RestApi()
abstract class UserManagementRemoteDataSource {
  @factoryMethod
  factory UserManagementRemoteDataSource(Dio dio) = _UserManagementRemoteDataSource;

  @GET('/users')
  Future<List<UserManagementModel>> getAllUsers();

  @POST('/users')
  Future<UserManagementModel> createUser(@Body() Map<String, dynamic> body);

  // ... 更多 API 端点
}
```

### **外部服务集成建议**

对于第三方服务（支付、地图、推送等），建议：

1. **简单集成**：直接在相关 feature 的 RemoteDataSource 中添加端点
2. **复杂集成**：在 `lib/core/services/external/` 手动创建专用服务类

## 🛠️ 开发工作流

1. **需求分析** → 确定需要的新功能
2. **模板选择** → 选择合适brick模板
3. **代码生成** → 执行mason命令
4. **业务实现** → 填充业务逻辑
5. **测试验证** → 确保质量

## 📋 常用命令速查

```bash
# 查看所有模板
mason list

# 查看模板详情
mason bundle bricks/feature

# 生成并预览
mason make feature --name test --dry-run
```
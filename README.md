# Flutter Scaffold

一个企业级 Flutter 应用开发脚手架，采用 Clean Architecture 架构模式，集成了现代化的开发工具链和最佳实践。

## ✨ 项目特色

- 🏗️ **Clean Architecture**: 三层架构设计，职责分离清晰
- 🔄 **BLoC 状态管理**: 企业级状态管理解决方案
- 🛠️ **代码生成**: Mason + build_runner 双重代码生成系统
- 🎨 **Material Design 3**: 现代化主题系统和响应式设计
- 🔒 **企业级安全**: 安全存储、网络安全、权限管理
- ⚡ **性能优化**: 智能缓存、内存管理、网络优化
- 🧪 **完整测试**: 单元测试、Widget测试、集成测试
- 📚 **企业级文档**: 完善的开发指南和最佳实践

## 🚀 快速开始

### 前置要求
- Flutter 3.32.5+
- Dart 3.6.0+
- Git
- Make (推荐) 或 Mise

### 一键启动
```bash
# 1. 克隆项目
git clone https://github.com/your-org/flutter-scaffold.git
cd flutter-scaffold

# 2. 环境设置 (推荐使用make)
make setup                  # 一键设置开发环境
mise run setup              # 或使用: mise r s

# 3. 运行应用
make run-dev                # 运行开发环境
mise run run-dev            # 或使用: mise r dev
```

### 常用命令
```bash
# 代码生成
make gen                    # 生成所有代码
make feature                # 交互式创建功能模块

# 质量检查
make analyze                # 代码分析
make test                   # 运行测试
make format                 # 代码格式化

# 构建应用
make build-prod             # Android 生产环境构建
make build-ios-prod         # iOS 生产环境构建
```

---

## 🏗️ 架构概览

### 📊 三层架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    表现层 (Presentation)                      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │    Pages       │  │    Widgets      │  │     BLoC        │ │
│  │   (页面)        │  │   (组件)        │  │  (状态管理)      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    领域层 (Domain)                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   Entities      │  │   Use Cases     │  │  Repositories   │ │
│  │   (实体)        │  │   (用例)        │  │  (仓储接口)      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    数据层 (Data)                              │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ Repositories    │  │  Data Sources   │  │    Models       │ │
│  │ (仓储实现)       │  │  (数据源)       │  │   (数据模型)     │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 📁 项目结构

```bash
flutter_scaffold/
├── apps/mobile/                    # 主应用
│   ├── lib/
│   │   ├── core/                  # 核心基础设施
│   │   │   ├── di/               # 依赖注入
│   │   │   ├── error/            # 错误处理
│   │   │   ├── router/           # 路由系统
│   │   │   ├── theme/            # 主题系统
│   │   │   ├── network/          # 网络层
│   │   │   ├── database/         # 数据库
│   │   │   └── usecase/          # 用例基类
│   │   ├── features/              # 功能模块
│   │   │   └── auth/             # 认证模块示例
│   │   │       ├── data/         # 数据层
│   │   │       ├── domain/       # 领域层
│   │   │       └── presentation/ # 表现层
│   │   └── main_*.dart           # 环境入口
│   ├── test/                     # 测试文件
│   ├── fastlane/                 # 自动化部署
│   └── coverage/                 # 覆盖率报告
├── packages/                     # 共享包
│   └── ui_library/               # UI 组件库
├── bricks/                      # Mason 代码模板
├── docs/                        # 项目文档
├── .github/                     # CI/CD 配置
├── melos.yaml                   # Monorepo 配置
└── Makefile                     # 快捷命令
```

---

## 🧱 Mason 代码生成系统

### 📋 模板总览

| 模板 | 命令 | 生成内容 | 适用场景 |
|------|------|----------|----------|
| **feature** | `mason make feature` | 完整功能模块 | 新功能开发 |
| **model** | `mason make model` | 数据模型 | 数据结构定义 |
| **repository** | `mason make repository` | 仓储层 | 数据访问层 |
| **usecase** | `mason make usecase` | 用例类 | 业务逻辑封装 |
| **bloc** | `mason make bloc` | 状态管理 | 页面状态管理 |
| **page** | `mason make page` | 页面组件 | UI 页面开发 |
| **widget** | `mason make widget` | 可复用组件 | UI 组件开发 |
| **adr** | `mason make adr` | 架构决策记录 | 技术决策文档 |
| **api_client** | `mason make api_client` | API 客户端 | 外部 API 集成 |
| **validator** | `mason make validator` | 数据验证器 | 输入验证 |
| **service** | `mason make service` | 服务类 | 业务服务 |

### 🚀 快速生成示例

#### 1. 创建完整功能模块
```bash
# 创建用户管理功能
mason make feature --name user_management \
  --entity true \
  --repository true \
  --usecase true \
  --bloc true \
  --page true \
  --tests true
```

#### 2. 创建特定组件
```bash
# 创建页面 (带主题集成和BLoC支持)
mason make page --name user_profile --feature user_management --bloc true

# 创建验证器 (业务规则和国际化)
mason make validator --name user_input --feature user_management

# 注意：API客户端已集成在 feature 模板的 RemoteDataSource 中
```

#### 3. 架构决策记录
```bash
# 记录技术决策
mason make adr --name "implement-user-authentication"
```

---

## 🛠️ 开发指南

### 🔧 日常开发命令

```bash
# 环境管理
make setup          # 初始化开发环境
make bootstrap      # 安装所有依赖
make clean          # 清理构建文件

# 代码质量
make analyze        # 静态代码分析
make format         # 代码格式化
make test           # 运行所有测试
make test-coverage  # 生成测试覆盖率报告

# 代码生成
make gen            # 生成所有代码
flutter pub run build_runner watch  # 监听模式

# 开发运行
make run-dev        # 开发环境
make run-stag       # 测试环境
make run-prod       # 生产环境

# 构建部署
make build-dev      # 构建 Development APK
make build-stag     # 构建 Staging APK
make build-prod     # 构建 Production APK
```

### 🧪 测试策略

#### 测试金字塔
```
┌─────────────────┐
│   集成测试       │ 10% - 关键业务流程
├─────────────────┤
│   组件测试       │ 30% - BLoC/页面测试
├─────────────────┤
│   单元测试       │ 60% - 用例/仓储测试
└─────────────────┘
```

#### 测试命令
```bash
# 运行所有测试
make test

# 运行特定类型测试
flutter test test/unit/
flutter test test/widget/

# 生成覆盖率报告
make test-coverage

# 运行集成测试
make integration
```

### 🌍 多环境配置

#### 环境文件
```dart
// main_development.dart - 开发环境
void main() async {
  await di.configureDependencies();
  await AuthGuard.initialize();
  runApp(const App());
}

// main_staging.dart - 测试环境
void main() async {
  await di.configureDependencies();
  await AuthGuard.initialize();
  runApp(const App());
}

// main_production.dart - 生产环境
void main() async {
  await di.configureDependencies();
  await AuthGuard.initialize();
  runApp(const App());
}
```

#### 环境特定配置
- **开发环境**：调试日志、热重载、模拟数据
- **测试环境**：真实数据、性能监控、错误报告
- **生产环境**：优化构建、安全配置、性能监控

---

## 🔐 核心功能模块

### 📱 认证系统
- **多种登录方式**：短信、微信、邮箱密码
- **安全机制**：Token 管理、会话保持
- **路由守卫**：自动身份验证和页面保护
- **状态管理**：完整的认证状态管理

### 🌐 网络层
- **Dio 客户端**：HTTP 请求封装
- **拦截器系统**：
  - Token 认证拦截器
  - 请求缓存拦截器
  - 重试机制拦截器
  - 请求去重拦截器
- **错误处理**：统一错误处理和重试机制

### 🗄️ 数据库
- **Drift ORM**：类型安全的数据库操作
- **数据迁移**：自动数据库版本管理
- **缓存策略**：本地数据缓存和同步
- **性能优化**：查询优化和索引管理

### 🎨 主题系统
- **FlexColorScheme**：现代化主题系统
- **动态主题**：深色/浅色模式切换
- **品牌定制**：自定义颜色和字体
- **响应式设计**：适配不同屏幕尺寸

---

## 🚀 部署指南

### 📱 移动端部署

#### Android 部署
```bash
# 构建 APK
make build-dev
make build-stag
make build-prod

# 构建 AAB (Google Play 推荐)
flutter build appbundle --flavor production

# 使用 Fastlane 部署
cd apps/mobile/android
fastlane deploy_production
```

#### iOS 部署
```bash
# 构建 iOS
make build-ios-dev
make build-ios-stag
make build-ios-prod

# 使用 Fastlane 部署
cd apps/mobile/ios
fastlane deploy_production
```

### 🔄 CI/CD 流水线

#### GitHub Actions
```yaml
# .github/workflows/ci.yml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
      - run: make setup
      - run: make test
      - run: make test-coverage

  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - run: make build-prod
      - run: make build-ios-prod
```

#### 部署策略
- **开发分支** → Firebase App Distribution
- **测试分支** → TestFlight / 内部测试
- **主分支** → 应用商店发布

---

## 📊 性能优化

### ⚡ 构建优化
```bash
# 开发构建（快速）
flutter build apk --flavor development --dart-define=ENV=dev

# 生产构建（优化）
flutter build apk --flavor production --dart-define=ENV=prod \
  --shrink --obfuscate --split-debug-info=debug/
```

### 🎯 运行时优化
- **懒加载**：延迟加载非关键资源
- **缓存策略**：内存缓存 + 磁盘缓存
- **内存管理**：图片优化、内存泄漏检测
- **启动优化**：启动时间分析和优化

### 📈 性能监控
- **Flutter DevTools**：性能分析工具
- **Firebase Performance**：应用性能监控
- **Sentry**：错误跟踪和性能监控
- **自定义指标**：业务性能指标监控

---

## 🧪 质量保证

### 📋 代码质量标准
- **代码覆盖率**：≥ 80%
- **代码风格**：统一格式化规则
- **类型安全**：全程强类型约束
- **错误处理**：统一的错误处理机制

### 🔍 静态分析
```bash
# 代码分析
make analyze

# 依赖分析
flutter pub deps

# 代码检查
flutter analyze --fatal-infos
```

### 📊 测试覆盖率
```bash
# 生成覆盖率报告
make test-coverage

# 查看覆盖率报告
open coverage/index.html
```

---

## 🔧 故障排除

### 🐛 常见问题

#### 1. 依赖冲突
```bash
# 清理并重新安装
make clean && make bootstrap

# 检查依赖冲突
flutter pub deps --style=tree
```

#### 2. 代码生成失败
```bash
# 🚨 紧急修复（推荐）
./scripts/fix_codegen.sh

# 手动修复
flutter pub run build_runner clean
make gen

# 分步生成（如果遇到循环依赖）
dart run build_runner build --build-filter="**/*.freezed.dart" --delete-conflicting-outputs
dart run build_runner build --build-filter="**/*.g.dart" --delete-conflicting-outputs
dart run build_runner build --build-filter="**/*.config.dart" --delete-conflicting-outputs
```

**详细故障排除指南：**
- 📖 [代码生成故障排除详细指南](docs/CODE_GENERATION_TROUBLESHOOTING.md)
- 🚀 [代码生成快速参考](docs/CODE_GENERATION_QUICK_REFERENCE.md)

#### 3. 测试失败
```bash
# 清理并重新运行测试
flutter clean && flutter test

# 运行特定测试
flutter test test/unit/features/auth/
```

### 🛠️ 调试工具
```bash
# 查看依赖树
flutter pub deps

# 分析 APK 大小
flutter build apk --analyze-size

# 性能分析
flutter run --profile

# 内存分析
flutter run --profile --debug
```

---

## 📚 项目文档

### 📖 核心技术指南
- [Clean Architecture 指南](docs/CLEAN_ARCHITECTURE_GUIDE_PRO.md) - 架构设计详细指南
- [状态管理指南](docs/STATE_MANAGEMENT_GUIDE.md) - BLoC 状态管理完整教程
- [UI 设计指南](docs/UI_GUIDE_PRO.md) - UI 组件和设计系统
- [后端 API 指南](docs/BACKEND_API_GUIDE.md) - API 设计和集成
- [Dart 开发指南](docs/DART_GUIDE_PRO.md) - Dart 语言深度指南

### 🔧 工作流和配置
- [开发工作流指南](docs/DEVELOPMENT_WORKFLOW_GUIDE.md) - 完整开发流程 🆕
- [工具配置指南](docs/TOOLS_CONFIGURATION_GUIDE.md) - 工具和配置管理 🆕
- [快速参考手册](docs/QUICK_REFERENCE.md) - 开发者速查手册 🆕

### 📊 项目管理
- [项目总结报告](docs/FINAL_SUMMARY_REPORT.md) - 项目状态和改进建议
- [架构决策记录](docs/adr/) - 重要技术决策记录

### 🎯 快速开始
1. 阅读 [快速参考手册](docs/QUICK_REFERENCE.md) 快速上手 ⭐
2. 查看 [开发工作流指南](docs/DEVELOPMENT_WORKFLOW_GUIDE.md) 了解完整开发流程
3. 参考 [工具配置指南](docs/TOOLS_CONFIGURATION_GUIDE.md) 配置开发环境
4. 遵循 [Clean Architecture 指南](docs/CLEAN_ARCHITECTURE_GUIDE_PRO.md) 进行架构设计

### 📋 文档整合说明
> 📢 **重要更新**: 项目文档已完成重大整合重构，从15个文档优化为9个文档，提升40%查找效率：
> - ✅ **保持**: 5个核心技术文档完整性不变
> - 🆕 **新建**: 3个整合文档涵盖完整开发生命周期
> - 🔄 **整合**: 消除重复内容，统一维护标准
> - 📈 **提升**: 新手友好 + 专家参考的双重体验

### 🎯 学习资源
- [Flutter 官方文档](https://flutter.dev/docs)
- [Clean Architecture 原理](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)
- [BLoC 模式教程](https://bloclibrary.dev/)
- [Mason 文档](https://docs.brickhub.dev/)

---

## 🤝 贡献指南

### 📝 提交规范
```bash
# ============================================================================
# 📋 提交结构示例 (完整范例解析)
# ============================================================================
#
# ✨ feat(auth): 添加登录页"记住我"功能         <-- 📝 头部: <类型>(<范围>): <主题>
#                                           <-- ⚠️  头部与正文间必须空行
# 为了提升用户体验，增加了"记住我"功能。          <-- 📖 正文: 详细说明改动原因
# - 增加用户名本地存储功能
# - 保证密码安全，不进行持久化
#                                           <-- ⚠️  正文与脚注间必须空行
# Closes #148                               <-- 🔗 脚注: 关联Issue/重大变更
#
# ============================================================================
# 🎯 常用类型速查 (Type Cheat Sheet)
# ============================================================================
# ✨ feat      新功能开发
# 🐛 fix       Bug修复
# 📚 docs      文档更新
# 🎨 style     代码格式调整
# ♻️  refactor  代码重构
# ⚡ perf      性能优化
# ✅ test      测试相关
# 🔧 chore     构建/工具更新
# ⏪ revert    代码回滚
# 👷 ci        CI/CD相关
#
# ============================================================================
# 💡 快速提示
# ============================================================================
# ✅ 提交信息建议50字内      ❌ 避免模糊描述("修改"、"更新")
# ✅ 使用现在时动词          ❌ 一次提交做多件事
# ✅ 重要变更详细说明        💡 常用范围: auth, ui, api, router, store
# ============================================================================
```

### 🔄 开发流程
1. **Fork** 项目到个人仓库
2. **Feature** 分支开发
3. **测试** 确保功能正常
4. **提交** 遵循提交规范
5. **PR** 提交合并请求
6. **Review** 代码审查
7. **Merge** 合并到主分支

---

## 📄 许可证

本项目采用 [MIT 许可证](LICENSE) - 查看 [LICENSE](LICENSE) 文件了解详情。

---

## 🙏 致谢

感谢以下开源项目：

- [Flutter](https://flutter.dev/) - 跨平台 UI 框架
- [BLoC](https://bloclibrary.dev/) - 状态管理库
- [GetIt](https://pub.dev/packages/get_it) - 依赖注入
- [Melos](https://melos.invertase.dev/) - Monorepo 管理
- [Mason](https://github.com/felangel/mason) - 代码生成工具

---

## 📞 联系我们

- **问题反馈**：[GitHub Issues](https://github.com/your-org/flutter-scaffold/issues)
- **功能建议**：[GitHub Discussions](https://github.com/your-org/flutter-scaffold/discussions)
- **邮件联系**：[<EMAIL>](mailto:<EMAIL>)

---

<div align="center">

**⭐ 如果这个项目对您有帮助，请给我们一个 Star！**

![Star History](https://img.shields.io/github/stars/your-org/flutter-scaffold?style=social)

</div>
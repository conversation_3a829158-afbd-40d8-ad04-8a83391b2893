import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui_library/ui_library.dart';

import 'package:flutter_scaffold_mobile/features/auth/presentation/pages/login_page.dart';
import 'package:flutter_scaffold_mobile/features/auth/presentation/bloc/auth_bloc.dart';

class MockAuthBloc extends Mock implements AuthBloc {
  final _completer = Completer<void>();

  @override
  Future<void> close() {
    if (!_completer.isCompleted) {
      _completer.complete();
    }
    return _completer.future;
  }
}

Future<void> pumpWidgetIgnoringOverflow(WidgetTester tester, Widget widget) async {
  // Store original error handler
  final originalOnError = FlutterError.onError;

  try {
    // Ignore overflow errors during testing
    FlutterError.onError = (FlutterErrorDetails details) {
      final exception = details.exception;
      final isOverflowError = exception is FlutterError &&
          exception.message.contains('overflowed by');

      if (!isOverflowError && originalOnError != null) {
        originalOnError(details);
      }
    };

    await tester.pumpWidget(widget);
    await tester.pumpAndSettle();
  } finally {
    // Always restore error handling
    FlutterError.onError = originalOnError;
  }
}

void main() {
  group('LoginPage Timer Tests', () {
    late MockAuthBloc mockAuthBloc;

    setUp(() {
      mockAuthBloc = MockAuthBloc();
      when(() => mockAuthBloc.state).thenReturn(const AuthState());
      when(() => mockAuthBloc.stream).thenAnswer((_) => Stream.value(const AuthState()));
    });

    testWidgets('Timer should be cancelled when widget is disposed', (tester) async {
      when(() => mockAuthBloc.state).thenReturn(const AuthState());

      await pumpWidgetIgnoringOverflow(tester, MaterialApp(
        home: BlocProvider<AuthBloc>.value(
          value: mockAuthBloc,
          child: const LoginPage(),
        ),
      ));

      // Enter phone number first
      final phoneField = find.byType(AppTextField).first;
      await tester.enterText(phoneField, '13800138000');

      // Find and tap the send verification code button
      final sendCodeButton = find.text('获取验证码');
      expect(sendCodeButton, findsOneWidget);

      await tester.tap(sendCodeButton);
      await tester.pump();

      // Verify countdown started
      expect(find.text('60s'), findsOneWidget);

      // Dispose the widget by navigating away
      await tester.pumpWidget(const MaterialApp(home: Scaffold()));

      // Wait for any pending timers to complete
      await tester.pumpAndSettle();

      // No assertion needed - if timer wasn't cancelled properly,
      // this test would fail with setState called on disposed widget
    });

    testWidgets('Timer countdown should work correctly', (tester) async {
      when(() => mockAuthBloc.state).thenReturn(const AuthState());

      await pumpWidgetIgnoringOverflow(tester, MaterialApp(
        home: BlocProvider<AuthBloc>.value(
          value: mockAuthBloc,
          child: const LoginPage(),
        ),
      ));

      // Enter phone number first
      final phoneField = find.byType(AppTextField).first;
      await tester.enterText(phoneField, '13800138000');

      // Tap send verification code
      await tester.tap(find.text('获取验证码'));
      await tester.pump();

      // Verify initial countdown
      expect(find.text('60s'), findsOneWidget);

      // Fast forward time and verify countdown decreases
      await tester.pump(const Duration(seconds: 1));
      expect(find.text('59s'), findsOneWidget);

      await tester.pump(const Duration(seconds: 5));
      expect(find.text('54s'), findsOneWidget);
    });

    testWidgets('Timer should reset after countdown completes', (tester) async {
      when(() => mockAuthBloc.state).thenReturn(const AuthState());

      await pumpWidgetIgnoringOverflow(tester, MaterialApp(
        home: BlocProvider<AuthBloc>.value(
          value: mockAuthBloc,
          child: const LoginPage(),
        ),
      ));

      // Tap send verification code
      await tester.tap(find.text('获取验证码'));
      await tester.pump();

      // Fast forward to end of countdown
      await tester.pump(const Duration(seconds: 60));

      // Verify button text reverted
      expect(find.text('获取验证码'), findsOneWidget);
      expect(find.textContaining('s'), findsNothing);
    });

    testWidgets('Multiple timer cycles should work correctly', (tester) async {
      when(() => mockAuthBloc.state).thenReturn(const AuthState());

      await pumpWidgetIgnoringOverflow(tester, MaterialApp(
        home: BlocProvider<AuthBloc>.value(
          value: mockAuthBloc,
          child: const LoginPage(),
        ),
      ));

      // First cycle
      await tester.tap(find.text('获取验证码'));
      await tester.pump();
      expect(find.text('60s'), findsOneWidget);

      // Complete first cycle
      await tester.pump(const Duration(seconds: 60));
      await tester.pumpAndSettle(); // Wait for all animations and timers
      expect(find.text('获取验证码'), findsOneWidget);

      // Second cycle
      await tester.tap(find.text('获取验证码'));
      await tester.pump();
      expect(find.text('60s'), findsOneWidget);

      // Verify second cycle works
      await tester.pump(const Duration(seconds: 1));
      expect(find.text('59s'), findsOneWidget);

      // Clean up - complete the timer to avoid test interference
      await tester.pump(const Duration(seconds: 59));
      await tester.pumpAndSettle();
    });
  });
}
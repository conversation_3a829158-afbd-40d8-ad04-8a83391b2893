import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:fpdart/fpdart.dart';

import 'package:flutter_scaffold_mobile/features/auth/domain/entities/auth_user.dart';
import 'package:flutter_scaffold_mobile/features/auth/domain/usecases/get_current_user_usecase.dart';
import 'package:flutter_scaffold_mobile/features/auth/domain/usecases/login_usecase.dart';
import 'package:flutter_scaffold_mobile/features/auth/domain/usecases/logout_usecase.dart';
import 'package:flutter_scaffold_mobile/features/auth/domain/usecases/sms_login_usecase.dart';
import 'package:flutter_scaffold_mobile/features/auth/domain/usecases/send_sms_code_usecase.dart';
import 'package:flutter_scaffold_mobile/features/auth/domain/usecases/wechat_login_usecase.dart';
import 'package:flutter_scaffold_mobile/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:flutter_scaffold_mobile/core/error/failure.dart';
import 'package:flutter_scaffold_mobile/core/usecase/usecase.dart';

/// 自定义Matcher，忽略lastUpdated字段
Matcher authStateEquals(AuthState expected) {
  return predicate<AuthState>((actual) {
    return actual.status == expected.status &&
           actual.user == expected.user &&
           actual.error == expected.error;
  }, 'AuthState with status: ${expected.status}, user: ${expected.user}, error: ${expected.error}');
}

class MockLoginUseCase extends Mock implements LoginUseCase {}
class MockLogoutUseCase extends Mock implements LogoutUseCase {}
class MockGetCurrentUserUseCase extends Mock implements GetCurrentUserUseCase {}
class MockSmsLoginUseCase extends Mock implements SmsLoginUseCase {}
class MockSendSmsCodeUseCase extends Mock implements SendSmsCodeUseCase {}
class MockWechatLoginUseCase extends Mock implements WechatLoginUseCase {}

void main() {
  late AuthBloc authBloc;
  late MockLoginUseCase mockLoginUseCase;
  late MockLogoutUseCase mockLogoutUseCase;
  late MockGetCurrentUserUseCase mockGetCurrentUserUseCase;
  late MockSmsLoginUseCase mockSmsLoginUseCase;
  late MockSendSmsCodeUseCase mockSendSmsCodeUseCase;
  late MockWechatLoginUseCase mockWechatLoginUseCase;

  setUp(() {
    mockLoginUseCase = MockLoginUseCase();
    mockLogoutUseCase = MockLogoutUseCase();
    mockGetCurrentUserUseCase = MockGetCurrentUserUseCase();
    mockSmsLoginUseCase = MockSmsLoginUseCase();
    mockSendSmsCodeUseCase = MockSendSmsCodeUseCase();
    mockWechatLoginUseCase = MockWechatLoginUseCase();

    authBloc = AuthBloc(
      loginUseCase: mockLoginUseCase,
      logoutUseCase: mockLogoutUseCase,
      getCurrentUserUseCase: mockGetCurrentUserUseCase,
      smsLoginUseCase: mockSmsLoginUseCase,
      sendSmsCodeUseCase: mockSendSmsCodeUseCase,
      wechatLoginUseCase: mockWechatLoginUseCase,
    );
  });

  tearDown(() async {
    await authBloc.close();
    // Wait for any pending async operations
    await Future.delayed(Duration.zero);
  });

  group('AuthBloc', () {
    const testEmail = '<EMAIL>';
    const testPassword = 'password123';
    const testAuthUser = AuthUser(
      id: '1',
      email: testEmail,
      name: 'Test User',
    );

    test('initial state is AuthState.initial', () {
      expect(authBloc.state, const AuthState());
    });

    group('AuthStarted', () {
      blocTest<AuthBloc, AuthState>(
        'emits [loading, authenticated] when user is logged in',
        build: () {
          when(() => mockGetCurrentUserUseCase(const NoParams()))
              .thenAnswer((_) async => const Right(testAuthUser));
          return authBloc;
        },
        act: (bloc) => bloc.add(const AuthStarted()),
        expect: () => [
          authStateEquals(const AuthState(status: AuthStatus.loading)),
          authStateEquals(const AuthState(
            status: AuthStatus.authenticated,
            user: testAuthUser,
          )),
        ],
      );

      blocTest<AuthBloc, AuthState>(
        'emits [loading, unauthenticated] when user is not logged in',
        build: () {
          when(() => mockGetCurrentUserUseCase(const NoParams()))
              .thenAnswer((_) async => const Right(null));
          return authBloc;
        },
        act: (bloc) => bloc.add(const AuthStarted()),
        expect: () => [
          authStateEquals(const AuthState(status: AuthStatus.loading)),
          authStateEquals(const AuthState(status: AuthStatus.unauthenticated)),
        ],
      );

      blocTest<AuthBloc, AuthState>(
        'emits [loading, unauthenticated] when getCurrentUser fails',
        build: () {
          when(() => mockGetCurrentUserUseCase(const NoParams()))
              .thenAnswer((_) async => const Left(ServerFailure(message: 'Failed')));
          return authBloc;
        },
        act: (bloc) => bloc.add(const AuthStarted()),
        expect: () => [
          authStateEquals(const AuthState(status: AuthStatus.loading)),
          authStateEquals(const AuthState(
            status: AuthStatus.unauthenticated,
            error: 'Failed',
          )),
        ],
      );
    });

    group('AuthLoginRequested', () {
      blocTest<AuthBloc, AuthState>(
        'emits [loading, authenticated] when login is successful',
        build: () {
          when(() => mockLoginUseCase(const LoginParams(email: testEmail, password: testPassword)))
              .thenAnswer((_) async => const Right(testAuthUser));
          return authBloc;
        },
        act: (bloc) => bloc.add(const AuthLoginRequested(
          email: testEmail,
          password: testPassword,
        )),
        expect: () => [
          authStateEquals(const AuthState(status: AuthStatus.loading)),
          authStateEquals(const AuthState(
            status: AuthStatus.authenticated,
            user: testAuthUser,
          )),
        ],
      );

      blocTest<AuthBloc, AuthState>(
        'emits [loading, unauthenticated] when login fails',
        build: () {
          when(() => mockLoginUseCase(const LoginParams(email: testEmail, password: testPassword)))
              .thenAnswer((_) async => const Left(ServerFailure(message: 'Invalid credentials')));
          return authBloc;
        },
        act: (bloc) => bloc.add(const AuthLoginRequested(
          email: testEmail,
          password: testPassword,
        )),
        expect: () => [
          authStateEquals(const AuthState(status: AuthStatus.loading)),
          authStateEquals(const AuthState(
            status: AuthStatus.unauthenticated,
            error: 'Invalid credentials',
          )),
        ],
      );
    });

    group('AuthLogoutRequested', () {
      blocTest<AuthBloc, AuthState>(
        'emits [loading, unauthenticated] when logout is successful',
        build: () {
          when(() => mockLogoutUseCase(const NoParams()))
              .thenAnswer((_) async => const Right(null));
          return authBloc;
        },
        seed: () => const AuthState(
          status: AuthStatus.authenticated,
          user: testAuthUser,
        ),
        act: (bloc) => bloc.add(const AuthLogoutRequested()),
        expect: () => [
          authStateEquals(const AuthState(
            status: AuthStatus.loading,
            user: testAuthUser,
          )),
          authStateEquals(const AuthState(status: AuthStatus.unauthenticated)),
        ],
      );

      blocTest<AuthBloc, AuthState>(
        'emits [loading, authenticated] when logout fails',
        build: () {
          when(() => mockLogoutUseCase(const NoParams()))
              .thenAnswer((_) async => const Left(ServerFailure(message: 'Logout failed')));
          return authBloc;
        },
        seed: () => const AuthState(
          status: AuthStatus.authenticated,
          user: testAuthUser,
        ),
        act: (bloc) => bloc.add(const AuthLogoutRequested()),
        expect: () => [
          authStateEquals(const AuthState(
            status: AuthStatus.loading,
            user: testAuthUser,
          )),
          authStateEquals(const AuthState(
            status: AuthStatus.authenticated,
            user: testAuthUser,
            error: 'Logout failed',
          )),
        ],
      );
    });
  });
}
// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mocktail/mocktail.dart';

import 'package:flutter_scaffold_mobile/core/theme/app_theme.dart';
import 'package:flutter_scaffold_mobile/features/auth/presentation/bloc/auth_bloc.dart';

class MockAuthBloc extends Mock implements AuthBloc {
  final _completer = Completer<void>();

  @override
  Future<void> close() {
    if (!_completer.isCompleted) {
      _completer.complete();
    }
    return _completer.future;
  }
}

void main() {
  testWidgets('App should render without crashing', (WidgetTester tester) async {
    final mockAuthBloc = MockAuthBloc();
    when(() => mockAuthBloc.state).thenReturn(const AuthState());
    when(() => mockAuthBloc.stream).thenAnswer((_) => Stream.value(const AuthState()));

    // Build a simplified version of our app without complex routing
    await tester.pumpWidget(
      BlocProvider<AuthBloc>(
        create: (_) => mockAuthBloc,
        child: MaterialApp(
          title: 'Flutter Scaffold',
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          themeMode: ThemeMode.system,
          home: const Scaffold(
            body: Center(
              child: Text('Flutter Scaffold App'),
            ),
          ),
          debugShowCheckedModeBanner: false,
        ),
      ),
    );

    // Verify that the app renders without crashing
    await tester.pumpAndSettle();
    expect(find.byType(MaterialApp), findsOneWidget);
    expect(find.text('Flutter Scaffold App'), findsOneWidget);
  });
}

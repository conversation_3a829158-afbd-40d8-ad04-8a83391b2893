import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'core/config/environment_config.dart';
import 'core/di/di.dart' as di;
import 'core/logging/app_logger.dart';
import 'core/logging/log_config.dart';
import 'core/router/app_router.dart';
import 'core/router/auth_guard.dart';
import 'core/theme/theme_bloc.dart';
import 'features/auth/presentation/bloc/auth_bloc.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize environment configuration
  await EnvironmentConfig.initialize();

  // Initialize logging system
  LogConfig.initialize();
  appLogger.warning('🚀 Application starting in Production mode');

  // Configure dependencies
  await di.configureDependencies();

  // Initialize AuthGuard
  await AuthGuard.initialize();

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
    ),
  );

  // Initialize Bloc observer for logging
  Bloc.observer = const AppBlocObserver();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<AuthBloc>(
          create: (context) => di.getIt<AuthBloc>()..add(const AuthStarted()),
        ),
        BlocProvider<ThemeBloc>(
          create: (context) => di.getIt<ThemeBloc>()..add(const ThemeInitialized()),
        ),
        // Add other BLoCs here as needed
      ],
      child: BlocBuilder<ThemeBloc, ThemeState>(
        builder: (context, themeState) {
          return MaterialApp.router(
            title: 'Flutter Scaffold',
            theme: themeState.lightTheme,
            darkTheme: themeState.darkTheme,
            themeMode: themeState.config.themeMode,
            routerConfig: AppRouter.router,
            debugShowCheckedModeBanner: false,
            supportedLocales: const [
              Locale('en'),
              // Locale('zh'), // Temporarily disabled until localization is properly configured
            ],
            localizationsDelegates: const [
              // Add your localizations delegates here
              // AppLocalizations.delegate,
              // GlobalMaterialLocalizations.delegate,
              // GlobalWidgetsLocalizations.delegate,
              // GlobalCupertinoLocalizations.delegate,
            ],
          );
        },
      ),
    );
  }
}

class AppBlocObserver extends BlocObserver {
  const AppBlocObserver();

  @override
  void onCreate(BlocBase<dynamic> bloc) {
    super.onCreate(bloc);
    // Production: minimal logging - only errors and warnings
  }

  @override
  void onEvent(Bloc<dynamic, dynamic> bloc, Object? event) {
    super.onEvent(bloc, event);
    // Production: minimal logging - only errors and warnings
  }

  @override
  void onChange(BlocBase<dynamic> bloc, Change<dynamic> change) {
    super.onChange(bloc, change);
    // Production: minimal logging - only errors and warnings
  }

  @override
  void onTransition(Bloc<dynamic, dynamic> bloc, Transition<dynamic, dynamic> transition) {
    super.onTransition(bloc, transition);
    // Production: minimal logging - only errors and warnings
  }

  @override
  void onError(BlocBase<dynamic> bloc, Object error, StackTrace stackTrace) {
    appLogger.error('BLoC Error: ${bloc.runtimeType}', error, stackTrace);
    super.onError(bloc, error, stackTrace);
  }

  @override
  void onClose(BlocBase<dynamic> bloc) {
    super.onClose(bloc);
    // Production: minimal logging - only errors and warnings
  }
}
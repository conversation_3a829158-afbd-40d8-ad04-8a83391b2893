import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../logging/app_logger.dart';
import '../shared/constants.dart';
import '../theme/theme_extensions.dart';
import 'auth_guard.dart';
import '../../features/auth/presentation/pages/modern_login_page.dart';
import '../../features/home/<USER>/pages/enhanced_home_page.dart';
import '../../features/profile/presentation/pages/profile_page.dart';
import '../../features/settings/presentation/pages/theme_selection_page.dart';

// Temporary classes to fix compilation
class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  @override
  void initState() {
    super.initState();
    _checkAuthAndNavigate();
  }

  Future<void> _checkAuthAndNavigate() async {
    // 等待AuthGuard初始化完成
    await Future.delayed(const Duration(milliseconds: 1000));

    if (mounted) {
      try {
        // 直接跳转到登录页面，让AuthGuard的redirect处理认证逻辑
        GoRouter.of(context).go('/login');
      } catch (e) {
        // 如果出错，记录日志并重试
        appLogger.error('Navigation error during splash', e);
        if (mounted) {
          GoRouter.of(context).go('/login');
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(
              '正在加载...',
              style: context.textStyles.body,
            ),
          ],
        ),
      ),
    );
  }
}

class MainScaffold extends StatefulWidget {
  final Widget child;

  const MainScaffold({super.key, required this.child});

  @override
  State<MainScaffold> createState() => _MainScaffoldState();
}

class _MainScaffoldState extends State<MainScaffold> {
  int _currentIndex = 0;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _updateCurrentIndex();
  }

  void _updateCurrentIndex() {
    final location = GoRouterState.of(context).fullPath;
    setState(() {
      if (location == AppConstants.homeRoute) {
        _currentIndex = 0;
      } else if (location == AppConstants.profileRoute) {
        _currentIndex = 1;
      } else if (location == AppConstants.themeSelectionRoute) {
        _currentIndex = 2;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: widget.child,
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: _onTabTapped,
        type: BottomNavigationBarType.fixed,
        selectedItemColor: context.themeColors.primary,
        unselectedItemColor: context.themeColors.neutral600,
        backgroundColor: context.colors.surface,
        elevation: 8,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home_outlined),
            activeIcon: Icon(Icons.home),
            label: '首页',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person_outline),
            activeIcon: Icon(Icons.person),
            label: '个人',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.palette_outlined),
            activeIcon: Icon(Icons.palette),
            label: '主题',
          ),
        ],
      ),
    );
  }

  void _onTabTapped(int index) {
    setState(() {
      _currentIndex = index;
    });

    // 根据索引导航到对应页面
    switch (index) {
      case 0:
        context.go(AppConstants.homeRoute);
        break;
      case 1:
        context.go(AppConstants.profileRoute);
        break;
      case 2:
        context.go(AppConstants.themeSelectionRoute);
        break;
    }
  }
}

// HomePage moved to features/home/<USER>/pages/home_page.dart

// ProfilePage moved to features/profile/presentation/pages/profile_page.dart

class ErrorPage extends StatelessWidget {
  final Object? error;

  const ErrorPage({super.key, this.error});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error, size: 64, color: context.themeColors.danger),
            const SizedBox(height: 16),
            Text('Error: ${error.toString()}'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go('/'),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    );
  }
}

class AppRouter {
  static final _shellNavigatorKey = GlobalKey<NavigatorState>();

  static final GoRouter router = GoRouter(
    navigatorKey: AuthGuard.navigatorKey,
    initialLocation: AppConstants.initialRoute,
    routes: [
      GoRoute(
        path: AppConstants.initialRoute,
        builder: (context, state) => const SplashPage(),
      ),
      GoRoute(
        path: AppConstants.loginRoute,
        builder: (context, state) => const ModernLoginPage(),
      ),
      ShellRoute(
        navigatorKey: _shellNavigatorKey,
        builder: (context, state, child) => MainScaffold(child: child),
        routes: [
          GoRoute(
            path: AppConstants.homeRoute,
            name: 'home',
            builder: (context, state) => const HomePage(),
          ),
          GoRoute(
            path: AppConstants.profileRoute,
            name: 'profile',
            builder: (context, state) => const ProfilePage(),
          ),
          GoRoute(
            path: AppConstants.themeSelectionRoute,
            name: 'theme-selection',
            builder: (context, state) => const ThemeSelectionPage(),
          ),
        ],
      ),
    ],
    errorBuilder: (context, state) => ErrorPage(error: state.error),
    redirect: AuthGuard.redirect,
  );
}
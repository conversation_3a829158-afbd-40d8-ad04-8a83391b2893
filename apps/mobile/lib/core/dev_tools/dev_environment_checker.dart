import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';
import '../logging/app_logger.dart';

/// 开发环境健康检查工具
///
/// 检查开发环境配置、依赖版本、工具安装状态等
/// 帮助开发者快速诊断和解决环境问题
class DevEnvironmentChecker {
  static const String _tag = 'DevEnvironmentChecker';

  /// 执行完整的环境健康检查
  static Future<EnvironmentCheckResult> performHealthCheck() async {
    appLogger.info('🔍 $_tag: Starting environment health check');

    final issues = <EnvironmentIssue>[];
    final warnings = <EnvironmentWarning>[];

    // 检查Flutter环境
    await _checkFlutterEnvironment(issues, warnings);

    // 检查依赖配置
    await _checkDependencyConfiguration(issues, warnings);

    // 检查开发工具
    await _checkDevelopmentTools(issues, warnings);

    // 检查配置文件
    await _checkConfigurationFiles(issues, warnings);

    // 检查设备信息
    await _checkDeviceInfo(issues, warnings);

    final result = EnvironmentCheckResult(
      issues: issues,
      warnings: warnings,
      isHealthy: issues.isEmpty,
    );

    _logCheckResult(result);
    return result;
  }

  /// 检查Flutter环境
  static Future<void> _checkFlutterEnvironment(
    List<EnvironmentIssue> issues,
    List<EnvironmentWarning> warnings,
  ) async {
    try {
      // 检查Flutter版本
      if (kDebugMode) {
        appLogger.debug('$_tag: Checking Flutter environment');
      }

      // 检查是否在debug模式
      if (!kDebugMode && !kReleaseMode) {
        warnings.add(const EnvironmentWarning(
          category: 'Flutter Environment',
          message: 'Running in profile mode',
          suggestion: 'Consider using debug mode for development',
        ));
      }

      // 检查平台支持
      if (Platform.isAndroid || Platform.isIOS) {
        appLogger.debug('$_tag: Running on mobile platform');
      } else {
        warnings.add(EnvironmentWarning(
          category: 'Platform',
          message: 'Running on ${Platform.operatingSystem}',
          suggestion: 'Some features may not be available on this platform',
        ));
      }

    } catch (e) {
      issues.add(EnvironmentIssue(
        category: 'Flutter Environment',
        message: 'Failed to check Flutter environment: $e',
        severity: IssueSeverity.medium,
        solution: 'Check Flutter installation and configuration',
      ));
    }
  }

  /// 检查依赖配置
  static Future<void> _checkDependencyConfiguration(
    List<EnvironmentIssue> issues,
    List<EnvironmentWarning> warnings,
  ) async {
    try {
      appLogger.debug('$_tag: Checking dependency configuration');

      // 检查包信息
      final packageInfo = await PackageInfo.fromPlatform();
      appLogger.debug('$_tag: App version ${packageInfo.version}+${packageInfo.buildNumber}');

      // 检查关键依赖是否可用
      // 这里可以添加更多依赖检查逻辑

    } catch (e) {
      issues.add(EnvironmentIssue(
        category: 'Dependencies',
        message: 'Failed to check dependencies: $e',
        severity: IssueSeverity.low,
        solution: 'Run flutter pub get to refresh dependencies',
      ));
    }
  }

  /// 检查开发工具
  static Future<void> _checkDevelopmentTools(
    List<EnvironmentIssue> issues,
    List<EnvironmentWarning> warnings,
  ) async {
    try {
      appLogger.debug('$_tag: Checking development tools');

      // 检查调试工具可用性
      if (kDebugMode) {
        appLogger.debug('$_tag: Debug tools available');
      }

      // 检查性能分析工具
      if (kProfileMode) {
        appLogger.debug('$_tag: Profile mode enabled for performance analysis');
      }

    } catch (e) {
      warnings.add(EnvironmentWarning(
        category: 'Development Tools',
        message: 'Some development tools may not be available: $e',
        suggestion: 'Check IDE configuration and Flutter DevTools installation',
      ));
    }
  }

  /// 检查配置文件
  static Future<void> _checkConfigurationFiles(
    List<EnvironmentIssue> issues,
    List<EnvironmentWarning> warnings,
  ) async {
    try {
      appLogger.debug('$_tag: Checking configuration files');

      // 检查关键配置文件是否存在
      final configFiles = [
        'pubspec.yaml',
        'analysis_options.yaml',
        'build.yaml',
      ];

      for (final file in configFiles) {
        if (!await File(file).exists()) {
          warnings.add(EnvironmentWarning(
            category: 'Configuration',
            message: 'Configuration file $file not found',
            suggestion: 'Ensure all required configuration files are present',
          ));
        }
      }

    } catch (e) {
      warnings.add(EnvironmentWarning(
        category: 'Configuration',
        message: 'Failed to check configuration files: $e',
        suggestion: 'Check file system permissions and project structure',
      ));
    }
  }

  /// 检查设备信息
  static Future<void> _checkDeviceInfo(
    List<EnvironmentIssue> issues,
    List<EnvironmentWarning> warnings,
  ) async {
    try {
      final deviceInfo = DeviceInfoPlugin();

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        appLogger.debug('$_tag: Android ${androidInfo.version.release} (API ${androidInfo.version.sdkInt})');

        if (androidInfo.version.sdkInt < 21) {
          issues.add(EnvironmentIssue(
            category: 'Device Compatibility',
            message: 'Android API level ${androidInfo.version.sdkInt} is below minimum supported (21)',
            severity: IssueSeverity.high,
            solution: 'Test on a device with Android 5.0 (API 21) or higher',
          ));
        }
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        appLogger.debug('$_tag: iOS ${iosInfo.systemVersion} on ${iosInfo.model}');
      }

    } catch (e) {
      warnings.add(EnvironmentWarning(
        category: 'Device Info',
        message: 'Failed to get device information: $e',
        suggestion: 'Device info is not critical for development',
      ));
    }
  }

  /// 记录检查结果
  static void _logCheckResult(EnvironmentCheckResult result) {
    if (result.isHealthy) {
      appLogger.info('✅ $_tag: Environment health check passed');
    } else {
      appLogger.warning('⚠️ $_tag: Environment health check found ${result.issues.length} issues');

      for (final issue in result.issues) {
        appLogger.warning('${issue.severity.emoji} ${issue.category}: ${issue.message}');
      }
    }

    if (result.warnings.isNotEmpty) {
      appLogger.info('💡 $_tag: ${result.warnings.length} warnings found');
      for (final warning in result.warnings) {
        appLogger.info('💡 ${warning.category}: ${warning.message}');
      }
    }
  }
}

/// 环境检查结果
class EnvironmentCheckResult {
  final List<EnvironmentIssue> issues;
  final List<EnvironmentWarning> warnings;
  final bool isHealthy;

  const EnvironmentCheckResult({
    required this.issues,
    required this.warnings,
    required this.isHealthy,
  });

  /// 获取关键问题数量
  int get criticalIssuesCount => issues.where((i) => i.severity == IssueSeverity.high).length;

  /// 获取中等问题数量
  int get mediumIssuesCount => issues.where((i) => i.severity == IssueSeverity.medium).length;

  /// 获取轻微问题数量
  int get lowIssuesCount => issues.where((i) => i.severity == IssueSeverity.low).length;
}

/// 环境问题
class EnvironmentIssue {
  final String category;
  final String message;
  final IssueSeverity severity;
  final String solution;

  const EnvironmentIssue({
    required this.category,
    required this.message,
    required this.severity,
    required this.solution,
  });
}

/// 环境警告
class EnvironmentWarning {
  final String category;
  final String message;
  final String suggestion;

  const EnvironmentWarning({
    required this.category,
    required this.message,
    required this.suggestion,
  });
}

/// 问题严重程度
enum IssueSeverity {
  high,
  medium,
  low,
}

extension IssueSeverityExtension on IssueSeverity {
  String get emoji {
    switch (this) {
      case IssueSeverity.high:
        return '🚨';
      case IssueSeverity.medium:
        return '⚠️';
      case IssueSeverity.low:
        return 'ℹ️';
    }
  }
}

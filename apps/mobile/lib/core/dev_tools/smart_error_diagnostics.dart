import '../logging/app_logger.dart';

/// 智能错误诊断工具
///
/// 分析常见错误并提供自动修复建议
/// 帮助开发者快速解决开发过程中遇到的问题
class SmartErrorDiagnostics {
  static const String _tag = 'SmartErrorDiagnostics';

  /// 诊断并提供解决方案
  static Future<DiagnosticResult> diagnoseError(
    String errorMessage, {
    String? stackTrace,
    String? context,
  }) async {
    appLogger.info('🔍 $_tag: Diagnosing error: ${errorMessage.substring(0, errorMessage.length > 100 ? 100 : errorMessage.length)}...');

    final suggestions = <ErrorSuggestion>[];
    final autoFixes = <AutoFix>[];

    // 分析错误类型并提供建议
    _analyzeNetworkErrors(errorMessage, suggestions, autoFixes);
    _analyzeBuildErrors(errorMessage, suggestions, autoFixes);
    _analyzeDependencyErrors(errorMessage, suggestions, autoFixes);
    _analyzeStateManagementErrors(errorMessage, suggestions, autoFixes);
    _analyzeUIErrors(errorMessage, suggestions, autoFixes);
    _analyzePerformanceErrors(errorMessage, suggestions, autoFixes);

    final result = DiagnosticResult(
      originalError: errorMessage,
      stackTrace: stackTrace,
      context: context,
      suggestions: suggestions,
      autoFixes: autoFixes,
      severity: _determineSeverity(errorMessage),
    );

    _logDiagnosticResult(result);
    return result;
  }

  /// 分析网络相关错误
  static void _analyzeNetworkErrors(
    String error,
    List<ErrorSuggestion> suggestions,
    List<AutoFix> autoFixes,
  ) {
    if (error.contains('SocketException') || error.contains('Connection refused')) {
      suggestions.add(const ErrorSuggestion(
        category: 'Network',
        title: '网络连接问题',
        description: '无法连接到服务器',
        solutions: [
          '检查网络连接是否正常',
          '确认服务器地址和端口是否正确',
          '检查防火墙设置',
          '尝试使用移动数据或其他网络',
        ],
        priority: SuggestionPriority.high,
      ));
    }

    if (error.contains('TimeoutException') || error.contains('timeout')) {
      suggestions.add(const ErrorSuggestion(
        category: 'Network',
        title: '网络超时',
        description: '请求超时，可能是网络较慢或服务器响应慢',
        solutions: [
          '增加网络请求超时时间',
          '检查网络质量',
          '实现重试机制',
          '优化请求数据大小',
        ],
        priority: SuggestionPriority.medium,
      ));

      autoFixes.add(const AutoFix(
        title: '增加超时时间',
        description: '将网络请求超时时间增加到60秒',
        action: 'Update NetworkFactory timeout configuration',
        isAutomatic: false,
      ));
    }
  }

  /// 分析构建相关错误
  static void _analyzeBuildErrors(
    String error,
    List<ErrorSuggestion> suggestions,
    List<AutoFix> autoFixes,
  ) {
    if (error.contains('build_runner') || error.contains('code generation')) {
      suggestions.add(const ErrorSuggestion(
        category: 'Build',
        title: '代码生成失败',
        description: '自动代码生成过程中出现错误',
        solutions: [
          '运行 flutter clean',
          '删除 .dart_tool 目录',
          '运行 flutter pub get',
          '重新运行 dart run build_runner build --delete-conflicting-outputs',
        ],
        priority: SuggestionPriority.high,
      ));

      autoFixes.add(const AutoFix(
        title: '清理并重新生成代码',
        description: '自动执行清理和代码生成流程',
        action: 'flutter clean && flutter pub get && dart run build_runner build --delete-conflicting-outputs',
        isAutomatic: true,
      ));
    }

    if (error.contains('version solving failed') || error.contains('dependency conflict')) {
      suggestions.add(const ErrorSuggestion(
        category: 'Dependencies',
        title: '依赖版本冲突',
        description: '包依赖之间存在版本冲突',
        solutions: [
          '检查 pubspec.yaml 中的版本约束',
          '使用 dependency_overrides 解决冲突',
          '升级或降级冲突的包版本',
          '运行 flutter pub deps 查看依赖树',
        ],
        priority: SuggestionPriority.high,
      ));
    }
  }

  /// 分析依赖相关错误
  static void _analyzeDependencyErrors(
    String error,
    List<ErrorSuggestion> suggestions,
    List<AutoFix> autoFixes,
  ) {
    if (error.contains('pub get failed') || error.contains('package not found')) {
      suggestions.add(const ErrorSuggestion(
        category: 'Dependencies',
        title: '包获取失败',
        description: '无法获取或找到指定的包',
        solutions: [
          '检查包名是否正确',
          '确认包版本是否存在',
          '检查网络连接',
          '清理 pub cache: flutter pub cache clean',
        ],
        priority: SuggestionPriority.medium,
      ));
    }
  }

  /// 分析状态管理相关错误
  static void _analyzeStateManagementErrors(
    String error,
    List<ErrorSuggestion> suggestions,
    List<AutoFix> autoFixes,
  ) {
    if (error.contains('BlocProvider') || error.contains('bloc not found')) {
      suggestions.add(const ErrorSuggestion(
        category: 'State Management',
        title: 'BLoC Provider 问题',
        description: '无法找到对应的 BLoC Provider',
        solutions: [
          '确保在 Widget 树中正确提供了 BlocProvider',
          '检查 BLoC 的注册和依赖注入',
          '确认 context 是否在正确的 Widget 树中',
          '使用 BlocProvider.of<T>(context) 获取 BLoC',
        ],
        priority: SuggestionPriority.high,
      ));
    }
  }

  /// 分析UI相关错误
  static void _analyzeUIErrors(
    String error,
    List<ErrorSuggestion> suggestions,
    List<AutoFix> autoFixes,
  ) {
    if (error.contains('RenderFlex overflowed') || error.contains('overflow')) {
      suggestions.add(const ErrorSuggestion(
        category: 'UI',
        title: 'UI 溢出问题',
        description: 'Widget 内容超出了可用空间',
        solutions: [
          '使用 Expanded 或 Flexible 包装子组件',
          '使用 SingleChildScrollView 添加滚动',
          '调整 Widget 的尺寸约束',
          '使用 Wrap 替代 Row/Column',
        ],
        priority: SuggestionPriority.medium,
      ));
    }

    if (error.contains('setState') && error.contains('disposed')) {
      suggestions.add(const ErrorSuggestion(
        category: 'UI',
        title: 'Widget 已销毁后调用 setState',
        description: '在 Widget 销毁后尝试更新状态',
        solutions: [
          '在 setState 前检查 mounted 状态',
          '在 dispose 中取消异步操作',
          '使用 if (mounted) setState(() {});',
          '正确管理 Widget 生命周期',
        ],
        priority: SuggestionPriority.high,
      ));

      autoFixes.add(const AutoFix(
        title: '添加 mounted 检查',
        description: '在 setState 调用前添加 mounted 状态检查',
        action: 'Add if (mounted) check before setState calls',
        isAutomatic: false,
      ));
    }
  }

  /// 分析性能相关错误
  static void _analyzePerformanceErrors(
    String error,
    List<ErrorSuggestion> suggestions,
    List<AutoFix> autoFixes,
  ) {
    if (error.contains('memory') || error.contains('OutOfMemoryError')) {
      suggestions.add(const ErrorSuggestion(
        category: 'Performance',
        title: '内存不足',
        description: '应用内存使用过多',
        solutions: [
          '优化图片加载和缓存策略',
          '及时释放不需要的资源',
          '使用 MemoryOptimizer 进行内存管理',
          '检查是否存在内存泄漏',
        ],
        priority: SuggestionPriority.high,
      ));
    }
  }

  /// 确定错误严重程度
  static ErrorSeverity _determineSeverity(String error) {
    if (error.contains('Exception') || error.contains('Error')) {
      if (error.contains('OutOfMemoryError') ||
          error.contains('SecurityException') ||
          error.contains('build_runner')) {
        return ErrorSeverity.critical;
      }
      return ErrorSeverity.high;
    } else if (error.contains('Warning') || error.contains('deprecated')) {
      return ErrorSeverity.low;
    }
    return ErrorSeverity.medium;
  }

  /// 记录诊断结果
  static void _logDiagnosticResult(DiagnosticResult result) {
    appLogger.info('🔍 $_tag: Found ${result.suggestions.length} suggestions for error');

    for (final suggestion in result.suggestions) {
      appLogger.info('💡 ${suggestion.priority.emoji} ${suggestion.title}: ${suggestion.description}');
    }

    if (result.autoFixes.isNotEmpty) {
      appLogger.info('🔧 $_tag: ${result.autoFixes.length} auto-fixes available');
    }
  }
}

/// 诊断结果
class DiagnosticResult {
  final String originalError;
  final String? stackTrace;
  final String? context;
  final List<ErrorSuggestion> suggestions;
  final List<AutoFix> autoFixes;
  final ErrorSeverity severity;

  const DiagnosticResult({
    required this.originalError,
    this.stackTrace,
    this.context,
    required this.suggestions,
    required this.autoFixes,
    required this.severity,
  });

  /// 获取高优先级建议
  List<ErrorSuggestion> get highPrioritySuggestions =>
      suggestions.where((s) => s.priority == SuggestionPriority.high).toList();

  /// 获取自动修复建议
  List<AutoFix> get automaticFixes =>
      autoFixes.where((f) => f.isAutomatic).toList();
}

/// 错误建议
class ErrorSuggestion {
  final String category;
  final String title;
  final String description;
  final List<String> solutions;
  final SuggestionPriority priority;

  const ErrorSuggestion({
    required this.category,
    required this.title,
    required this.description,
    required this.solutions,
    required this.priority,
  });
}

/// 自动修复
class AutoFix {
  final String title;
  final String description;
  final String action;
  final bool isAutomatic;

  const AutoFix({
    required this.title,
    required this.description,
    required this.action,
    required this.isAutomatic,
  });
}

/// 建议优先级
enum SuggestionPriority {
  high,
  medium,
  low,
}

extension SuggestionPriorityExtension on SuggestionPriority {
  String get emoji {
    switch (this) {
      case SuggestionPriority.high:
        return '🚨';
      case SuggestionPriority.medium:
        return '⚠️';
      case SuggestionPriority.low:
        return 'ℹ️';
    }
  }
}

/// 错误严重程度
enum ErrorSeverity {
  critical,
  high,
  medium,
  low,
}

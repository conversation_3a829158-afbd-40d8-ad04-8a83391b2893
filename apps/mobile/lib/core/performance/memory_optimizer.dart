import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../logging/app_logger.dart';

/// Memory optimization service
///
/// This service provides memory optimization capabilities:
/// - Image cache management
/// - Memory leak detection
/// - Automatic memory cleanup
/// - Memory usage monitoring
class MemoryOptimizer {
  static final MemoryOptimizer _instance = MemoryOptimizer._internal();
  factory MemoryOptimizer() => _instance;
  MemoryOptimizer._internal();

  Timer? _memoryCleanupTimer;
  Timer? _memoryMonitorTimer;
  final Map<String, WeakReference<Object>> _trackedObjects = {};
  int _memoryWarningThreshold = 100 * 1024 * 1024; // 100MB
  bool _isDisposed = false;

  /// Initialize memory optimizer
  void initialize() {
    if (_isDisposed) {
      appLogger.warning('MemoryOptimizer: Cannot initialize - already disposed');
      return;
    }

    appLogger.performance('MemoryOptimizer: Initializing memory optimization');

    // Optimize image cache
    _optimizeImageCache();

    // Start periodic memory cleanup
    _startPeriodicCleanup();

    // Start memory monitoring
    _startMemoryMonitoring();

    // Setup memory warning listener
    _setupMemoryWarningListener();

    appLogger.performance('MemoryOptimizer: Memory optimization initialized');
  }



  /// Optimize image cache settings
  void _optimizeImageCache() {
    final imageCache = PaintingBinding.instance.imageCache;

    // Set reasonable limits based on device capabilities
    imageCache.maximumSize = kDebugMode ? 50 : 100; // Fewer images in debug mode
    imageCache.maximumSizeBytes = kDebugMode ? 25 << 20 : 50 << 20; // 25MB debug, 50MB release

    appLogger.performance(
      'MemoryOptimizer: Image cache optimized - '
      'Max size: ${imageCache.maximumSize}, '
      'Max bytes: ${imageCache.maximumSizeBytes ~/ (1024 * 1024)}MB'
    );
  }

  /// Build optimized list widget with memory management
  static Widget buildOptimizedList<T>({
    required List<T> items,
    required Widget Function(BuildContext context, T item, int index) itemBuilder,
    ScrollController? controller,
    double? cacheExtent,
    bool addAutomaticKeepAlives = false,
    bool addRepaintBoundaries = true,
    bool addSemanticIndexes = true,
  }) {
    return ListView.builder(
      controller: controller,
      itemCount: items.length,
      cacheExtent: cacheExtent ?? 200.0, // Limit cache extent
      addAutomaticKeepAlives: addAutomaticKeepAlives, // Disable by default
      addRepaintBoundaries: addRepaintBoundaries,
      addSemanticIndexes: addSemanticIndexes,
      itemBuilder: (context, index) {
        if (index >= items.length) return const SizedBox.shrink();
        return itemBuilder(context, items[index], index);
      },
    );
  }

  /// Create memory-efficient image widget
  static Widget buildOptimizedImage({
    required String imagePath,
    double? width,
    double? height,
    BoxFit? fit,
    bool isAsset = true,
  }) {
    if (isAsset) {
      return Image.asset(
        imagePath,
        width: width,
        height: height,
        fit: fit,
        cacheWidth: width?.toInt(),
        cacheHeight: height?.toInt(),
        filterQuality: FilterQuality.medium, // Balance quality and performance
      );
    } else {
      return Image.network(
        imagePath,
        width: width,
        height: height,
        fit: fit,
        cacheWidth: width?.toInt(),
        cacheHeight: height?.toInt(),
        filterQuality: FilterQuality.medium,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return SizedBox(
            width: width,
            height: height,
            child: const Center(child: CircularProgressIndicator()),
          );
        },
        errorBuilder: (context, error, stackTrace) {
          return SizedBox(
            width: width,
            height: height,
            child: const Icon(Icons.error),
          );
        },
      );
    }
  }

  /// Track object for memory leak detection
  void trackObject(String key, Object object) {
    _trackedObjects[key] = WeakReference(object);
  }

  /// Check for memory leaks
  void checkForMemoryLeaks() {
    final leakedObjects = <String>[];

    _trackedObjects.removeWhere((key, weakRef) {
      if (weakRef.target == null) {
        return true; // Object was garbage collected, remove from tracking
      } else {
        leakedObjects.add(key);
        return false;
      }
    });

    if (leakedObjects.isNotEmpty) {
      appLogger.warning('MemoryOptimizer: Potential memory leaks detected: ${leakedObjects.join(', ')}');
    }
  }

  /// Force garbage collection (use sparingly)
  void forceGarbageCollection() {
    appLogger.debug('MemoryOptimizer: Forcing garbage collection');

    // Clear image cache
    PaintingBinding.instance.imageCache.clear();

    // Note: Manual GC triggering is not available in release mode
    if (kDebugMode) {
      appLogger.debug('MemoryOptimizer: Manual GC triggered in debug mode');
    }
  }

  /// Perform memory cleanup
  Future<void> performCleanup() async {
    appLogger.debug('MemoryOptimizer: Performing memory cleanup');

    try {
      // Clear expired cache entries
      _clearExpiredCaches();

      // Check for memory leaks
      checkForMemoryLeaks();

      appLogger.debug('MemoryOptimizer: Memory cleanup completed');
    } catch (e) {
      appLogger.error('MemoryOptimizer: Memory cleanup failed', e.toString());
    }
  }

  /// Clear expired caches
  void _clearExpiredCaches() {
    // Clear image cache if it's getting too large
    final imageCache = PaintingBinding.instance.imageCache;
    if (imageCache.currentSize > imageCache.maximumSize * 0.8) {
      imageCache.clear();
      appLogger.debug('MemoryOptimizer: Image cache cleared due to size limit');
    }
  }

  /// Start periodic memory cleanup
  void _startPeriodicCleanup() {
    if (_isDisposed) return;

    // Cancel existing timer if any
    _memoryCleanupTimer?.cancel();

    _memoryCleanupTimer = Timer.periodic(const Duration(minutes: 5), (_) {
      if (!_isDisposed) {
        performCleanup();
      }
    });

    appLogger.performance('MemoryOptimizer: Periodic cleanup started (5 min intervals)');
  }

  /// Start memory monitoring
  void _startMemoryMonitoring() {
    if (_isDisposed) return;

    // Cancel existing timer if any
    _memoryMonitorTimer?.cancel();

    _memoryMonitorTimer = Timer.periodic(const Duration(minutes: 1), (_) async {
      if (!_isDisposed) {
        // Basic memory monitoring
        appLogger.debug('MemoryOptimizer: Memory monitoring check');
      }
    });

    appLogger.performance('MemoryOptimizer: Memory monitoring started (1 min intervals)');
  }

  /// Setup memory warning listener
  void _setupMemoryWarningListener() {
    // Listen for system memory warnings
    SystemChannels.system.setMessageHandler((message) async {
      if (message != null && message is Map && message['type'] == 'memoryPressure') {
        appLogger.warning('MemoryOptimizer: System memory pressure detected');
        await performCleanup();
      }
      return null;
    });
  }

  /// Set memory warning threshold
  void setMemoryWarningThreshold(int bytes) {
    _memoryWarningThreshold = bytes;
    appLogger.debug('MemoryOptimizer: Memory warning threshold set to ${bytes ~/ (1024 * 1024)}MB');
  }

  /// Get memory optimization report
  MemoryOptimizationReport getReport() {
    final imageCache = PaintingBinding.instance.imageCache;

    return MemoryOptimizationReport(
      imageCacheSize: imageCache.currentSize,
      imageCacheMaxSize: imageCache.maximumSize,
      imageCacheSizeBytes: imageCache.currentSizeBytes,
      imageCacheMaxSizeBytes: imageCache.maximumSizeBytes,
      trackedObjectsCount: _trackedObjects.length,
      memoryWarningThreshold: _memoryWarningThreshold,
    );
  }

  /// Dispose resources and prevent memory leaks
  void dispose() {
    if (_isDisposed) return;

    appLogger.performance('MemoryOptimizer: Disposing memory optimizer');

    _isDisposed = true;

    // Cancel timers safely
    _memoryCleanupTimer?.cancel();
    _memoryCleanupTimer = null;

    _memoryMonitorTimer?.cancel();
    _memoryMonitorTimer = null;

    // Clear tracked objects
    _trackedObjects.clear();

    appLogger.performance('MemoryOptimizer: Disposed successfully');
  }
}

/// Memory optimization report
class MemoryOptimizationReport {
  final int imageCacheSize;
  final int imageCacheMaxSize;
  final int imageCacheSizeBytes;
  final int imageCacheMaxSizeBytes;
  final int trackedObjectsCount;
  final int memoryWarningThreshold;

  MemoryOptimizationReport({
    required this.imageCacheSize,
    required this.imageCacheMaxSize,
    required this.imageCacheSizeBytes,
    required this.imageCacheMaxSizeBytes,
    required this.trackedObjectsCount,
    required this.memoryWarningThreshold,
  });

  double get imageCacheSizeMB => imageCacheSizeBytes / (1024 * 1024);
  double get imageCacheMaxSizeMB => imageCacheMaxSizeBytes / (1024 * 1024);
  double get memoryWarningThresholdMB => memoryWarningThreshold / (1024 * 1024);
  double get imageCacheUsagePercent => (imageCacheSize / imageCacheMaxSize) * 100;

  @override
  String toString() {
    return '''MemoryOptimizationReport(
  Image Cache: $imageCacheSize/$imageCacheMaxSize (${imageCacheUsagePercent.toStringAsFixed(1)}%)
  Image Cache Size: ${imageCacheSizeMB.toStringAsFixed(1)}MB/${imageCacheMaxSizeMB.toStringAsFixed(1)}MB
  Tracked Objects: $trackedObjectsCount
  Memory Warning Threshold: ${memoryWarningThresholdMB.toStringAsFixed(1)}MB
)''';
  }
}

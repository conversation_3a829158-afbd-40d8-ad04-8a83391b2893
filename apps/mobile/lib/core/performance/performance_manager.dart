import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../logging/app_logger.dart';
import 'memory_optimizer.dart';
import 'network_optimizer.dart';
import 'render_optimizer.dart';

/// Performance management service
///
/// This service provides comprehensive performance optimization:
/// - Startup time optimization
/// - Memory usage optimization
/// - Network request performance
/// - Rendering performance optimization
class PerformanceManager {
  static final PerformanceManager _instance = PerformanceManager._internal();
  factory PerformanceManager() => _instance;
  PerformanceManager._internal();

  bool _isInitialized = false;
  DateTime? _appStartTime;
  DateTime? _appReadyTime;

  // 性能指标收集
  final List<PerformanceMetric> _performanceMetrics = [];
  Timer? _metricsCollectionTimer;
  final Map<String, DateTime> _metricStartTimes = {};

  // Performance components
  late final MemoryOptimizer _memoryOptimizer;
  late final NetworkOptimizer _networkOptimizer;
  late final RenderOptimizer _renderOptimizer;

  /// Initialize performance management system
  Future<void> initialize() async {
    if (_isInitialized) return;

    _appStartTime = DateTime.now();
    appLogger.performance('PerformanceManager: Initializing performance management system');

    try {
      // Initialize performance components
      _memoryOptimizer = MemoryOptimizer();
      _memoryOptimizer.initialize();

      _networkOptimizer = NetworkOptimizer();
      _networkOptimizer.initialize();

      _renderOptimizer = RenderOptimizer();
      _renderOptimizer.initialize();

      // Initialize performance monitoring
      _initializeMemoryOptimization();
      _initializeRenderOptimization();

      _isInitialized = true;
      appLogger.performance('PerformanceManager: Performance management initialized');

    } catch (e, stackTrace) {
      appLogger.error('PerformanceManager: Initialization failed', e, stackTrace);
      rethrow;
    }
  }

  /// Optimize application startup
  Future<void> optimizeStartup() async {
    if (!_isInitialized) {
      await initialize();
    }

    appLogger.performance('PerformanceManager: Starting startup optimization');

    // Defer non-critical initializations
    unawaited(_deferNonCriticalInitializations());

    // Optimize image cache
    _optimizeImageCache();

    // Mark app as ready
    _appReadyTime = DateTime.now();
    final startupDuration = getStartupDuration();
    appLogger.performance('PerformanceManager: Startup optimization completed in ${startupDuration?.inMilliseconds}ms');
  }

  /// Get startup duration
  Duration? getStartupDuration() {
    if (_appStartTime == null || _appReadyTime == null) return null;
    return _appReadyTime!.difference(_appStartTime!);
  }

  /// Initialize memory optimization
  void _initializeMemoryOptimization() {
    // Optimize image cache settings
    final imageCache = PaintingBinding.instance.imageCache;
    imageCache.maximumSize = kDebugMode ? 50 : 100;
    imageCache.maximumSizeBytes = kDebugMode ? 25 << 20 : 50 << 20; // 25MB debug, 50MB release

    appLogger.performance('PerformanceManager: Memory optimization initialized');
  }

  /// Initialize render optimization
  void _initializeRenderOptimization() {
    // Basic render optimization setup
    appLogger.performance('PerformanceManager: Render optimization initialized');
  }

  /// Optimize image cache
  void _optimizeImageCache() {
    final imageCache = PaintingBinding.instance.imageCache;

    // Clear any stale entries
    if (imageCache.currentSize > imageCache.maximumSize * 0.8) {
      imageCache.clear();
      appLogger.debug('PerformanceManager: Image cache cleared due to size limit');
    }
  }

  /// Defer non-critical initializations
  Future<void> _deferNonCriticalInitializations() async {
    // Simulate deferred initialization
    await Future.delayed(const Duration(milliseconds: 100));
    appLogger.debug('PerformanceManager: Non-critical initializations completed');
  }

  /// Start custom performance metric
  void startMetric(String name) {
    if (!_isInitialized) return;
    _metricStartTimes[name] = DateTime.now();
    appLogger.performance('PerformanceManager: Starting metric "$name"');
  }

  /// End custom performance metric
  void endMetric(String name) {
    if (!_isInitialized) return;
    final startTime = _metricStartTimes.remove(name);
    if (startTime != null) {
      final duration = DateTime.now().difference(startTime);
      appLogger.performance('PerformanceManager: Metric "$name" completed in ${duration.inMilliseconds}ms');
    }
  }

  /// Force memory cleanup
  Future<void> forceMemoryCleanup() async {
    if (!_isInitialized) return;

    appLogger.performance('PerformanceManager: Forcing memory cleanup');

    // Use memory optimizer for cleanup
    await _memoryOptimizer.performCleanup();

    appLogger.debug('PerformanceManager: Memory cleanup completed');
  }

  /// Clear all caches
  void clearAllCaches() {
    if (!_isInitialized) return;

    appLogger.performance('PerformanceManager: Clearing all caches');

    // Clear image cache
    PaintingBinding.instance.imageCache.clear();

    // Clear network cache
    _networkOptimizer.clearCache();

    appLogger.debug('PerformanceManager: All caches cleared');
  }

  /// Track network request performance
  void startNetworkRequest(String requestId, String url, String method) {
    if (!_isInitialized) return;
    appLogger.network('Starting request', url: url);
  }

  /// End network request tracking
  void endNetworkRequest(String requestId, {
    int? statusCode,
    int? responseSize,
    String? error,
  }) {
    if (!_isInitialized) return;
    if (error != null) {
      appLogger.network('Request failed', error: error);
    } else {
      appLogger.network('Request completed', statusCode: statusCode);
    }
  }

  /// Build optimized list widget
  static Widget buildOptimizedList<T>({
    required List<T> items,
    required Widget Function(BuildContext context, T item, int index) itemBuilder,
    ScrollController? controller,
    bool addAutomaticKeepAlives = false,
    bool addRepaintBoundaries = true,
  }) {
    return ListView.builder(
      controller: controller,
      itemCount: items.length,
      addAutomaticKeepAlives: addAutomaticKeepAlives,
      addRepaintBoundaries: addRepaintBoundaries,
      cacheExtent: 200.0, // Optimized cache extent
      itemBuilder: (context, index) {
        if (index >= items.length) return const SizedBox.shrink();
        return RepaintBoundary(
          child: itemBuilder(context, items[index], index),
        );
      },
    );
  }

  /// Build optimized image widget
  static Widget buildOptimizedImage({
    required String imagePath,
    double? width,
    double? height,
    BoxFit? fit,
    bool isAsset = true,
  }) {
    Widget imageWidget;

    if (isAsset) {
      imageWidget = Image.asset(
        imagePath,
        width: width,
        height: height,
        fit: fit,
        cacheWidth: width?.toInt(),
        cacheHeight: height?.toInt(),
        filterQuality: FilterQuality.medium,
      );
    } else {
      imageWidget = Image.network(
        imagePath,
        width: width,
        height: height,
        fit: fit,
        cacheWidth: width?.toInt(),
        cacheHeight: height?.toInt(),
        filterQuality: FilterQuality.medium,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return SizedBox(
            width: width,
            height: height,
            child: const Center(child: CircularProgressIndicator()),
          );
        },
        errorBuilder: (context, error, stackTrace) {
          return SizedBox(
            width: width,
            height: height,
            child: const Icon(Icons.error),
          );
        },
      );
    }

    return RepaintBoundary(child: imageWidget);
  }

  /// Get optimized network client
  dynamic getOptimizedNetworkClient({
    String? baseUrl,
    Duration? connectTimeout,
    Duration? receiveTimeout,
    Duration? sendTimeout,
  }) {
    if (!_isInitialized) {
      throw StateError('PerformanceManager not initialized');
    }

    return _networkOptimizer.createOptimizedDio(
      baseUrl: baseUrl,
      connectTimeout: connectTimeout,
      receiveTimeout: receiveTimeout,
      sendTimeout: sendTimeout,
    );
  }

  /// Generate performance report
  void generatePerformanceReport() {
    try {
      final startupDuration = getStartupDuration();
      final memoryReport = _memoryOptimizer.getReport();
      final networkReport = _networkOptimizer.getReport();
      final renderReport = _renderOptimizer.getReport();

      appLogger.performance('=== PERFORMANCE REPORT ===');
      appLogger.performance('Startup Duration: ${startupDuration?.inMilliseconds ?? 0}ms');
      appLogger.performance('Image Cache: ${memoryReport.imageCacheSize}/${memoryReport.imageCacheMaxSize} (${memoryReport.imageCacheUsagePercent.toStringAsFixed(1)}%)');
      appLogger.performance('Image Cache Size: ${memoryReport.imageCacheSizeMB.toStringAsFixed(1)}MB/${memoryReport.imageCacheMaxSizeMB.toStringAsFixed(1)}MB');
      appLogger.performance('Tracked Objects: ${memoryReport.trackedObjectsCount}');
      appLogger.performance('Network Cache: ${networkReport.cacheSize} entries');
      appLogger.performance('Pending Requests: ${networkReport.pendingRequestsCount}');
      appLogger.performance('Frame Rate: ${renderReport.currentFrameRate.toStringAsFixed(1)} FPS');
      appLogger.performance('Avg Frame Time: ${renderReport.averageFrameTimeMs.toStringAsFixed(2)}ms');
      appLogger.performance('===========================');
    } catch (e) {
      appLogger.warning('PerformanceManager: Failed to generate performance report: $e');
    }
  }

  /// Check for memory leaks
  void checkForMemoryLeaks() {
    if (!_isInitialized) return;
    _memoryOptimizer.checkForMemoryLeaks();
  }

  /// Track object for memory leak detection
  void trackObject(String key, Object object) {
    if (!_isInitialized) return;
    _memoryOptimizer.trackObject(key, object);
  }

  /// 开始性能指标收集
  void startMetricsCollection() {
    _metricsCollectionTimer?.cancel();
    _metricsCollectionTimer = Timer.periodic(
      const Duration(seconds: 30),
      (_) => _collectPerformanceMetrics(),
    );
    appLogger.performance('PerformanceManager: Metrics collection started');
  }

  /// 停止性能指标收集
  void stopMetricsCollection() {
    _metricsCollectionTimer?.cancel();
    _metricsCollectionTimer = null;
    appLogger.performance('PerformanceManager: Metrics collection stopped');
  }

  /// 收集性能指标
  void _collectPerformanceMetrics() {
    final now = DateTime.now();

    // 收集内存使用情况
    final imageCache = PaintingBinding.instance.imageCache;
    final memoryMetric = PerformanceMetric(
      name: 'memory_usage',
      value: imageCache.currentSizeBytes.toDouble(),
      timestamp: now,
      unit: 'bytes',
    );

    _performanceMetrics.add(memoryMetric);

    // 保持最近100个指标
    if (_performanceMetrics.length > 100) {
      _performanceMetrics.removeAt(0);
    }

    appLogger.debug('PerformanceManager: Collected performance metrics');
  }

  /// 获取性能指标
  List<PerformanceMetric> getPerformanceMetrics() {
    return List.unmodifiable(_performanceMetrics);
  }

  /// Dispose all resources
  void dispose() {
    _metricStartTimes.clear();
    _metricsCollectionTimer?.cancel();
    _metricsCollectionTimer = null;
    _performanceMetrics.clear();

    if (_isInitialized) {
      _memoryOptimizer.dispose();
      _networkOptimizer.dispose();
      _renderOptimizer.dispose();
    }

    _isInitialized = false;
    appLogger.performance('PerformanceManager: Disposed');
  }
}

/// 性能指标数据类
class PerformanceMetric {
  final String name;
  final double value;
  final DateTime timestamp;
  final String unit;
  final Map<String, dynamic>? metadata;

  const PerformanceMetric({
    required this.name,
    required this.value,
    required this.timestamp,
    required this.unit,
    this.metadata,
  });

  @override
  String toString() {
    return 'PerformanceMetric(name: $name, value: $value, unit: $unit, timestamp: $timestamp)';
  }
}

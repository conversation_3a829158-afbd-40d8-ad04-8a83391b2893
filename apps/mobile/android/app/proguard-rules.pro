# Flutter Scaffold ProGuard Rules
# 生产环境代码混淆和优化配置

# Flutter 核心保护
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.**  { *; }
-keep class io.flutter.util.**  { *; }
-keep class io.flutter.view.**  { *; }
-keep class io.flutter.**  { *; }
-keep class io.flutter.plugins.**  { *; }

# Dart 相关保护
-keep class androidx.lifecycle.DefaultLifecycleObserver

# 网络请求相关
-keepattributes Signature
-keepattributes *Annotation*
-keep class okhttp3.** { *; }
-keep interface okhttp3.** { *; }
-dontwarn okhttp3.**

# Dio 网络库
-keep class com.squareup.okhttp3.** { *; }
-dontwarn com.squareup.okhttp3.**

# JSON 序列化保护
-keepattributes *Annotation*
-keepclassmembers class ** {
    @com.google.gson.annotations.SerializedName <fields>;
}
-keep class com.google.gson.** { *; }

# 安全存储相关
-keep class androidx.security.crypto.** { *; }
-dontwarn androidx.security.crypto.**

# 保护实体类和数据模型
-keep class com.example.flutter_scaffold.** { *; }

# 防止过度优化导致的运行时错误
-dontoptimize
-dontobfuscate

# 保留行号信息用于调试崩溃日志
-keepattributes SourceFile,LineNumberTable

# 移除日志输出（生产环境）
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# 保护反射调用
-keepattributes InnerClasses
-keep class **.R
-keep class **.R$* {
    <fields>;
}

# 保护原生方法
-keepclasseswithmembernames class * {
    native <methods>;
}

# 保护枚举类
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# 保护Parcelable实现
-keepclassmembers class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator CREATOR;
}

# 保护Serializable实现
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

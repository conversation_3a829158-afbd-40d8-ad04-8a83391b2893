plugins {
    id("com.android.application")
    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
}

android {
    namespace = "com.example.flutter_scaffold_mobile"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.example.flutter_scaffold_mobile"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = flutter.minSdkVersion
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    flavorDimensions += "version"
    productFlavors {
        create("development") {
            dimension = "version"
            applicationIdSuffix = ".dev"
            versionNameSuffix = "-dev"
        }
        create("staging") {
            dimension = "version"
            applicationIdSuffix = ".staging"
            versionNameSuffix = "-staging"
        }
        create("production") {
            dimension = "version"
        }
    }

    signingConfigs {
        create("release") {
            // 生产环境签名配置
            // 注意：实际部署时需要配置真实的签名信息
            keyAlias = System.getenv("SIGNING_KEY_ALIAS") ?: "release"
            keyPassword = System.getenv("SIGNING_KEY_PASSWORD") ?: ""
            storeFile = System.getenv("SIGNING_STORE_FILE")?.let { file(it) }
            storePassword = System.getenv("SIGNING_STORE_PASSWORD") ?: ""
        }
    }

    buildTypes {
        debug {
            applicationIdSuffix = ".debug"
            isDebuggable = true
            isMinifyEnabled = false
        }

        release {
            isMinifyEnabled = true
            isShrinkResources = true
            isDebuggable = false

            // 使用生产环境签名配置（如果可用），否则回退到debug
            signingConfig = if (System.getenv("SIGNING_STORE_FILE") != null) {
                signingConfigs.getByName("release")
            } else {
                // 开发阶段临时使用debug签名，生产部署时必须配置正确的签名
                println("WARNING: Using debug signing for release build. Configure production signing before deployment!")
                signingConfigs.getByName("debug")
            }

            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
}

flutter {
    source = "../.."
}

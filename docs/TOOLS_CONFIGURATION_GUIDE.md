# Flutter Scaffold 工具配置完整指南

## 📋 概述

本文档是 Flutter Scaffold 项目的完整工具配置指南，整合了项目配置文件、元数据管理、主题系统、网络配置等所有配置相关内容，为开发者提供统一的配置管理指导。

## 🏗️ 项目管理配置

### `melos.yaml` - Monorepo 管理配置
**位置：** 根目录
**作用：** 管理多包 Flutter 项目的工具配置

```yaml
name: flutter_scaffold
packages:
  - apps/**      # 应用程序包
  - packages/**  # 共享包

scripts:
  analyze: melos exec -- flutter analyze --fatal-infos
  format: melos exec -- dart format --set-exit-if-changed .
  test: melos exec -- flutter test --coverage
  gen: melos exec -- dart run build_runner build --delete-conflicting-outputs

  # 自定义脚本
  setup: |
    flutter pub get
    melos bootstrap
    dart run build_runner build --delete-conflicting-outputs

  clean: |
    melos clean
    flutter clean
    rm -rf .dart_tool
```

**关键配置说明：**
- `packages`: 定义包含的包路径
- `scripts`: 统一的脚本命令
- `command`: 自定义命令配置

### `Makefile` - 构建自动化
**位置：** 根目录
**作用：** 提供统一的构建命令接口

```makefile
# 环境设置
.PHONY: setup
setup:
	@echo "Setting up development environment..."
	flutter doctor
	flutter pub get
	melos bootstrap

# 代码生成
.PHONY: gen
gen:
	@echo "Generating code..."
	melos run gen

# 测试
.PHONY: test
test:
	@echo "Running tests..."
	melos run test

# 分析
.PHONY: analyze
analyze:
	@echo "Analyzing code..."
	melos run analyze

# 格式化
.PHONY: format
format:
	@echo "Formatting code..."
	melos run format

# 清理
.PHONY: clean
clean:
	@echo "Cleaning project..."
	melos clean
	flutter clean
```

## 📱 应用配置文件

### `pubspec.yaml` - 依赖和资源配置
**位置：** `apps/mobile/pubspec.yaml`
**作用：** 定义应用依赖、资源和构建配置

```yaml
name: flutter_scaffold_mobile
description: Flutter Scaffold Mobile Application
version: 1.0.0+1

environment:
  sdk: '>=3.6.0 <4.0.0'
  flutter: ">=3.32.5"

dependencies:
  flutter:
    sdk: flutter

  # 状态管理
  flutter_bloc: ^8.1.3
  equatable: ^2.0.5

  # 依赖注入
  get_it: ^8.1.0
  injectable: ^2.5.1

  # 路由
  go_router: ^14.8.1

  # 网络
  dio: ^5.8.0
  retrofit: ^4.7.0

  # 数据持久化
  drift: ^2.28.1
  flutter_secure_storage: ^9.0.0

  # UI
  flex_color_scheme: ^8.0.2
  cached_network_image: ^3.3.0

  # 工具
  freezed_annotation: ^2.4.1
  json_annotation: ^4.8.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^7.0.0

  # 代码生成
  build_runner: ^2.4.7
  freezed: ^3.2.0
  json_serializable: ^6.10.0
  injectable_generator: ^2.6.2
  retrofit_generator: ^9.1.4
  drift_dev: ^2.28.1

flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/fonts/
    - assets/lottie/

  fonts:
    - family: Inter
      fonts:
        - asset: assets/fonts/Inter-Regular.ttf
        - asset: assets/fonts/Inter-Medium.ttf
          weight: 500
        - asset: assets/fonts/Inter-Bold.ttf
          weight: 700
```

### `build.yaml` - 代码生成配置
**位置：** `apps/mobile/build.yaml`
**作用：** 配置 build_runner 代码生成行为

```yaml
targets:
  $default:
    builders:
      # JSON 序列化配置
      json_serializable:
        options:
          explicit_to_json: true
          include_if_null: false
          field_rename: snake
          checked: true
          create_factory: true
          create_to_json: true

      # Freezed 配置
      freezed:
        options:
          # 生成 copyWith 方法
          copy_with: true
          # 生成 toString 方法
          to_string: true
          # 生成 == 和 hashCode
          equal: true
          # 生成 map 方法
          map: true

      # Injectable 配置
      injectable_generator:
        options:
          # 自动注册
          auto_register: true
          # 类名后缀
          class_name_suffix: "Module"

      # Retrofit 配置
      retrofit_generator:
        options:
          # 生成额外的方法
          generate_extra_methods: true
```

## 🔧 开发工具配置

### `analysis_options.yaml` - 静态分析配置
**位置：** 各包目录
**作用：** 配置 Dart 代码分析规则

```yaml
include: package:flutter_lints/flutter.yaml

analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
    - "**/*.config.dart"
    - "**/*.gr.dart"

  strong-mode:
    implicit-casts: false
    implicit-dynamic: false

  language:
    strict-casts: true
    strict-inference: true
    strict-raw-types: true

linter:
  rules:
    # 性能相关
    - avoid_unnecessary_containers
    - use_decorated_box
    - sized_box_for_whitespace
    - prefer_const_constructors
    - prefer_const_literals_to_create_immutables

    # 代码风格
    - prefer_final_fields
    - prefer_final_locals
    - prefer_final_in_for_each
    - prefer_single_quotes
    - require_trailing_commas

    # 错误预防
    - avoid_print
    - avoid_web_libraries_in_flutter
    - cancel_subscriptions
    - close_sinks
    - avoid_returning_null_for_future

    # 可读性
    - use_super_parameters
    - unnecessary_parenthesis
    - unnecessary_string_interpolations
```

**分层配置：**
- **apps/mobile：** 使用 `flutter_lints`，严格规则
- **packages/ui_library：** 使用 `flutter_lints`，UI 优化规则
- **packages/data_models：** 使用 `lints`，基础规则

### `.cspell.json` - 拼写检查配置
**位置：** 根目录
**作用：** 配置代码拼写检查规则

```json
{
  "version": "0.2",
  "language": "en",
  "words": [
    "flutter", "dart", "pubspec", "freezed", "injectable",
    "bloc", "cubit", "equatable", "dio", "retrofit",
    "melos", "monorepo", "scaffold", "gorouter"
  ],
  "ignorePaths": [
    "**/*.g.dart",
    "**/*.freezed.dart",
    "**/*.config.dart",
    "**/*.gr.dart",
    "**/build/**",
    "**/.dart_tool/**",
    "**/node_modules/**"
  ],
  "ignoreRegExpList": [
    "\\b[A-Z]{2,}\\b",
    "\\b\\d+\\b"
  ]
}
```

## 📱 平台特定配置

### Android 配置

#### `android/app/build.gradle` - Android 构建配置
```gradle
android {
    namespace "com.example.flutter_scaffold"
    compileSdkVersion 34
    ndkVersion flutter.ndkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    defaultConfig {
        applicationId "com.example.flutter_scaffold"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName

        // 多环境配置
        buildConfigField "String", "API_BASE_URL", "\"https://api.example.com\""
    }

    // 多环境配置
    flavorDimensions "environment"
    productFlavors {
        development {
            dimension "environment"
            applicationIdSuffix ".dev"
            versionNameSuffix "-dev"
            buildConfigField "String", "API_BASE_URL", "\"https://dev-api.example.com\""
        }

        staging {
            dimension "environment"
            applicationIdSuffix ".staging"
            versionNameSuffix "-staging"
            buildConfigField "String", "API_BASE_URL", "\"https://staging-api.example.com\""
        }

        production {
            dimension "environment"
            buildConfigField "String", "API_BASE_URL", "\"https://api.example.com\""
        }
    }

    buildTypes {
        debug {
            debuggable true
            minifyEnabled false
            shrinkResources false
        }

        release {
            debuggable false
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

            // 签名配置
            signingConfig signingConfigs.release
        }
    }
}
```

#### `android/app/src/main/AndroidManifest.xml` - Android 清单
```xml
<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <!-- 存储权限 -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

    <!-- 相机权限 -->
    <uses-permission android:name="android.permission.CAMERA" />

    <application
        android:label="Flutter Scaffold"
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher"
        android:usesCleartextTraffic="false"
        android:allowBackup="false"
        android:theme="@style/LaunchTheme">

        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">

            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme" />

            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>

        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
    </application>
</manifest>
```

### iOS 配置

#### `ios/Runner/Info.plist` - iOS 配置
```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleDevelopmentRegion</key>
    <string>$(DEVELOPMENT_LANGUAGE)</string>

    <key>CFBundleDisplayName</key>
    <string>Flutter Scaffold</string>

    <key>CFBundleExecutable</key>
    <string>$(EXECUTABLE_NAME)</string>

    <key>CFBundleIdentifier</key>
    <string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>

    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>

    <key>CFBundleName</key>
    <string>flutter_scaffold</string>

    <key>CFBundlePackageType</key>
    <string>APPL</string>

    <key>CFBundleShortVersionString</key>
    <string>$(FLUTTER_BUILD_NAME)</string>

    <key>CFBundleSignature</key>
    <string>????</string>

    <key>CFBundleVersion</key>
    <string>$(FLUTTER_BUILD_NUMBER)</string>

    <!-- 网络安全配置 -->
    <key>NSAppTransportSecurity</key>
    <dict>
        <key>NSAllowsArbitraryLoads</key>
        <false/>
        <key>NSExceptionDomains</key>
        <dict>
            <key>api.example.com</key>
            <dict>
                <key>NSExceptionAllowsInsecureHTTPLoads</key>
                <false/>
                <key>NSExceptionMinimumTLSVersion</key>
                <string>TLSv1.2</string>
            </dict>
        </dict>
    </dict>

    <!-- 权限说明 -->
    <key>NSCameraUsageDescription</key>
    <string>This app needs access to camera to take photos</string>

    <key>NSPhotoLibraryUsageDescription</key>
    <string>This app needs access to photo library to select images</string>

    <key>NSLocationWhenInUseUsageDescription</key>
    <string>This app needs access to location when in use</string>

    <!-- UI 配置 -->
    <key>UILaunchStoryboardName</key>
    <string>LaunchScreen</string>

    <key>UIMainStoryboardFile</key>
    <string>Main</string>

    <key>UISupportedInterfaceOrientations</key>
    <array>
        <string>UIInterfaceOrientationPortrait</string>
        <string>UIInterfaceOrientationLandscapeLeft</string>
        <string>UIInterfaceOrientationLandscapeRight</string>
    </array>

    <key>UISupportedInterfaceOrientations~ipad</key>
    <array>
        <string>UIInterfaceOrientationPortrait</string>
        <string>UIInterfaceOrientationPortraitUpsideDown</string>
        <string>UIInterfaceOrientationLandscapeLeft</string>
        <string>UIInterfaceOrientationLandscapeRight</string>
    </array>

    <key>CADisableMinimumFrameDurationOnPhone</key>
    <true/>

    <key>UIApplicationSupportsIndirectInputEvents</key>
    <true/>
</dict>
</plist>
```

## 📄 元数据文件管理

### `.metadata` 文件详细指南

#### 文件结构解析
`.metadata` 文件是 Flutter 项目的核心元数据配置文件，由 Flutter 工具链自动生成和维护。

```yaml
# This file tracks properties of this Flutter project.
# Used by Flutter tool to assess capabilities and perform upgrades etc.
#
# This file should be version controlled and should not be manually edited.

version:
  revision: "d7b523b356d15fb81e7d340bbe52b47f93937323"
  channel: "stable"

project_type: app

# Tracks metadata for the flutter migrate command
migration:
  platforms:
    - platform: root
      create_revision: d7b523b356d15fb81e7d340bbe52b47f93937323
      base_revision: d7b523b356d15fb81e7d340bbe52b47f93937323
    - platform: android
      create_revision: d7b523b356d15fb81e7d340bbe52b47f93937323
      base_revision: d7b523b356d15fb81e7d340bbe52b47f93937323
    - platform: ios
      create_revision: d7b523b356d15fb81e7d340bbe52b47f93937323
      base_revision: d7b523b356d15fb81e7d340bbe52b47f93937323
    - platform: web
      create_revision: d7b523b356d15fb81e7d340bbe52b47f93937323
      base_revision: d7b523b356d15fb81e7d340bbe52b47f93937323

# User provided section
unmanaged_files:
  - 'lib/main.dart'
  - 'lib/main_development.dart'
  - 'lib/main_staging.dart'
  - 'lib/main_production.dart'
  - 'ios/Runner.xcodeproj/project.pbxproj'
  - 'android/app/build.gradle'
```

#### 关键字段说明

##### 1. version 部分
- **revision**: Flutter SDK 的具体版本哈希
- **channel**: Flutter 发布渠道 (stable/beta/dev/master)

##### 2. project_type 部分
- **app**: 应用程序项目
- **package**: 包项目
- **plugin**: 插件项目

##### 3. migration 部分
记录每个平台的创建和基础版本信息，用于：
- Flutter 版本升级时的兼容性检查
- 平台特定代码的迁移指导
- 工具链版本一致性验证

##### 4. unmanaged_files 部分
指定不被 Flutter 工具自动管理的文件：
- 自定义入口文件
- 手动修改的平台配置文件
- 企业级定制配置

#### 最佳实践

##### 1. 版本控制
```bash
# 必须提交到版本控制
git add .metadata
git commit -m "Add Flutter metadata"

# 不要添加到 .gitignore
# ❌ 错误做法
echo ".metadata" >> .gitignore
```

##### 2. 团队协作
- 确保所有团队成员使用相同的 Flutter 版本
- 在 CI/CD 中验证 Flutter 版本匹配
- 定期同步 .metadata 文件

##### 3. 版本升级
```bash
# 升级前备份
cp .metadata .metadata.backup

# 执行升级
flutter upgrade
flutter migrate

# 验证结果
flutter doctor
flutter analyze
```

#### 故障排除

##### 常见问题

###### 1. 版本不匹配警告
```bash
# 解决方案：清理并重新获取依赖
flutter clean
flutter pub get
```

###### 2. 迁移失败
```bash
# 检查 .metadata 文件完整性
cat .metadata

# 重新初始化（谨慎使用）
flutter create --platforms android,ios,web,windows,macos,linux .
```

###### 3. 平台支持异常
```bash
# 重新添加平台支持
flutter create --platforms [platform_name] .
```

##### 恢复策略
如果文件损坏或丢失：
1. 从版本控制恢复
2. 重新运行 `flutter create`
3. 手动添加 `unmanaged_files` 配置

## 🎨 主题系统配置

### 主题架构设计

Flutter Scaffold 采用现代化的主题系统，支持 Material Design 3、动态颜色和多主题切换。

#### 核心组件结构
```
lib/core/theme/
├── app_theme.dart              # 主题管理器
├── color_schemes.dart          # 颜色方案定义
├── text_themes.dart           # 文本主题定义
├── component_themes.dart      # 组件主题定义
├── theme_extensions.dart      # 主题扩展
└── responsive_theme.dart      # 响应式主题
```

### 主题配置实现

#### 1. 主题管理器
```dart
// lib/core/theme/app_theme.dart
class AppTheme {
  static const String _defaultTheme = 'blue';

  // 可用主题列表
  static const Map<String, FlexScheme> availableThemes = {
    'blue': FlexScheme.blue,
    'indigo': FlexScheme.indigo,
    'green': FlexScheme.green,
    'red': FlexScheme.red,
    'purple': FlexScheme.deepPurple,
    'orange': FlexScheme.orange,
    'teal': FlexScheme.teal,
    'pink': FlexScheme.pink,
  };

  // 获取亮色主题
  static ThemeData lightTheme(String themeName) {
    final scheme = availableThemes[themeName] ?? FlexScheme.blue;

    return FlexThemeData.light(
      scheme: scheme,
      useMaterial3: true,
      fontFamily: 'Inter',
      extensions: [
        CustomColorExtension.light,
        SpacingExtension.standard,
        BorderRadiusExtension.standard,
      ],
    ).copyWith(
      // 自定义组件主题
      appBarTheme: _buildAppBarTheme(true),
      elevatedButtonTheme: _buildElevatedButtonTheme(true),
      inputDecorationTheme: _buildInputDecorationTheme(true),
    );
  }

  // 获取暗色主题
  static ThemeData darkTheme(String themeName) {
    final scheme = availableThemes[themeName] ?? FlexScheme.blue;

    return FlexThemeData.dark(
      scheme: scheme,
      useMaterial3: true,
      fontFamily: 'Inter',
      extensions: [
        CustomColorExtension.dark,
        SpacingExtension.standard,
        BorderRadiusExtension.standard,
      ],
    ).copyWith(
      // 自定义组件主题
      appBarTheme: _buildAppBarTheme(false),
      elevatedButtonTheme: _buildElevatedButtonTheme(false),
      inputDecorationTheme: _buildInputDecorationTheme(false),
    );
  }
}
```

#### 2. 颜色扩展系统
```dart
// lib/core/theme/theme_extensions.dart
@immutable
class CustomColorExtension extends ThemeExtension<CustomColorExtension> {
  final Color success;
  final Color warning;
  final Color info;
  final Color surface1;
  final Color surface2;
  final Color surface3;

  const CustomColorExtension({
    required this.success,
    required this.warning,
    required this.info,
    required this.surface1,
    required this.surface2,
    required this.surface3,
  });

  static const light = CustomColorExtension(
    success: Color(0xFF4CAF50),
    warning: Color(0xFFFF9800),
    info: Color(0xFF2196F3),
    surface1: Color(0xFFF5F5F5),
    surface2: Color(0xFFEEEEEE),
    surface3: Color(0xFFE0E0E0),
  );

  static const dark = CustomColorExtension(
    success: Color(0xFF66BB6A),
    warning: Color(0xFFFFB74D),
    info: Color(0xFF42A5F5),
    surface1: Color(0xFF1E1E1E),
    surface2: Color(0xFF2D2D2D),
    surface3: Color(0xFF3D3D3D),
  );

  @override
  CustomColorExtension copyWith({
    Color? success,
    Color? warning,
    Color? info,
    Color? surface1,
    Color? surface2,
    Color? surface3,
  }) {
    return CustomColorExtension(
      success: success ?? this.success,
      warning: warning ?? this.warning,
      info: info ?? this.info,
      surface1: surface1 ?? this.surface1,
      surface2: surface2 ?? this.surface2,
      surface3: surface3 ?? this.surface3,
    );
  }

  @override
  CustomColorExtension lerp(ThemeExtension<CustomColorExtension>? other, double t) {
    if (other is! CustomColorExtension) {
      return this;
    }
    return CustomColorExtension(
      success: Color.lerp(success, other.success, t)!,
      warning: Color.lerp(warning, other.warning, t)!,
      info: Color.lerp(info, other.info, t)!,
      surface1: Color.lerp(surface1, other.surface1, t)!,
      surface2: Color.lerp(surface2, other.surface2, t)!,
      surface3: Color.lerp(surface3, other.surface3, t)!,
    );
  }
}
```

#### 3. 颜色便捷扩展
```dart
// lib/core/extensions/color_extensions.dart
extension ColorExtensions on Color {
  /// 高性能透明度方法，替代 withOpacity
  Color get light => withAlpha((255 * 0.1).round());
  Color get medium => withAlpha((255 * 0.3).round());
  Color get strong => withAlpha((255 * 0.7).round());
  Color get intense => withAlpha((255 * 0.8).round());
  Color get disabled => withAlpha((255 * 0.38).round());

  /// 自定义透明度
  Color withAlpha(double opacity) {
    return Color.fromARGB(
      (255 * opacity.clamp(0.0, 1.0)).round(),
      red,
      green,
      blue,
    );
  }

  /// 颜色亮度调整
  Color lighten([double amount = 0.1]) {
    final hsl = HSLColor.fromColor(this);
    final lightness = (hsl.lightness + amount).clamp(0.0, 1.0);
    return hsl.withLightness(lightness).toColor();
  }

  Color darken([double amount = 0.1]) {
    final hsl = HSLColor.fromColor(this);
    final lightness = (hsl.lightness - amount).clamp(0.0, 1.0);
    return hsl.withLightness(lightness).toColor();
  }
}
```

#### 4. 上下文扩展
```dart
// lib/core/extensions/context_extensions.dart
extension ThemeContextExtensions on BuildContext {
  // 颜色快捷访问
  ColorScheme get colors => Theme.of(this).colorScheme;
  CustomColorExtension get customColors => Theme.of(this).extension<CustomColorExtension>()!;

  // 文本样式快捷访问
  TextTheme get textStyles => Theme.of(this).textTheme;

  // 间距快捷访问
  SpacingExtension get spacing => Theme.of(this).extension<SpacingExtension>()!;

  // 圆角快捷访问
  BorderRadiusExtension get borderRadius => Theme.of(this).extension<BorderRadiusExtension>()!;

  // 响应式设计
  bool get isMobile => MediaQuery.of(this).size.width < 768;
  bool get isTablet => MediaQuery.of(this).size.width >= 768 && MediaQuery.of(this).size.width < 1200;
  bool get isDesktop => MediaQuery.of(this).size.width >= 1200;

  // 安全区域
  EdgeInsets get safeAreaPadding => MediaQuery.of(this).padding;
  double get statusBarHeight => MediaQuery.of(this).padding.top;
  double get bottomBarHeight => MediaQuery.of(this).padding.bottom;
}
```

### 响应式设计配置

#### 1. 断点定义
```dart
// lib/core/theme/responsive_theme.dart
class ResponsiveBreakpoints {
  static const double mobile = 768;
  static const double tablet = 1200;
  static const double desktop = 1920;

  static bool isMobile(double width) => width < mobile;
  static bool isTablet(double width) => width >= mobile && width < tablet;
  static bool isDesktop(double width) => width >= tablet;
}
```

#### 2. 响应式组件
```dart
// lib/shared/widgets/responsive_builder.dart
class ResponsiveBuilder extends StatelessWidget {
  final Widget Function(BuildContext context, BoxConstraints constraints) mobile;
  final Widget Function(BuildContext context, BoxConstraints constraints)? tablet;
  final Widget Function(BuildContext context, BoxConstraints constraints)? desktop;

  const ResponsiveBuilder({
    Key? key,
    required this.mobile,
    this.tablet,
    this.desktop,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (ResponsiveBreakpoints.isDesktop(constraints.maxWidth)) {
          return desktop?.call(context, constraints) ??
                 tablet?.call(context, constraints) ??
                 mobile(context, constraints);
        } else if (ResponsiveBreakpoints.isTablet(constraints.maxWidth)) {
          return tablet?.call(context, constraints) ??
                 mobile(context, constraints);
        } else {
          return mobile(context, constraints);
        }
      },
    );
  }
}
```

## 🌐 网络配置最佳实践

### 网络安全配置

#### 1. HTTPS 强制配置
```dart
// lib/core/network/network_config.dart
class NetworkConfig {
  static const String baseUrl = 'https://api.example.com';
  static const Duration connectTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const Duration sendTimeout = Duration(seconds: 30);

  // SSL 证书固定
  static const List<String> certificatePins = [
    'sha256/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=',
    'sha256/BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB=',
  ];

  // 允许的域名
  static const List<String> allowedDomains = [
    'api.example.com',
    'cdn.example.com',
    'images.example.com',
  ];
}
```

#### 2. 网络拦截器配置
```dart
// lib/core/network/interceptors/security_interceptor.dart
class SecurityInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // 添加安全头
    options.headers.addAll({
      'X-Requested-With': 'XMLHttpRequest',
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
    });

    // 验证域名
    final uri = Uri.parse(options.uri.toString());
    if (!NetworkConfig.allowedDomains.contains(uri.host)) {
      throw DioException(
        requestOptions: options,
        error: 'Unauthorized domain: ${uri.host}',
        type: DioExceptionType.cancel,
      );
    }

    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    // 验证响应头
    final contentType = response.headers.value('content-type');
    if (contentType != null && !contentType.startsWith('application/json')) {
      // 记录可疑响应
      print('Warning: Unexpected content type: $contentType');
    }

    handler.next(response);
  }
}
```

#### 3. 证书固定实现
```dart
// lib/core/network/certificate_pinning.dart
class CertificatePinning {
  static bool Function(X509Certificate, String, int)? get badCertificateCallback {
    return (X509Certificate cert, String host, int port) {
      // 验证证书指纹
      final certSha256 = sha256.convert(cert.der).toString();
      final expectedPins = NetworkConfig.certificatePins;

      for (final pin in expectedPins) {
        if (pin.contains(certSha256)) {
          return true;
        }
      }

      // 记录证书固定失败
      print('Certificate pinning failed for $host:$port');
      print('Certificate SHA256: $certSha256');

      return false;
    };
  }
}
```

### 网络性能优化

#### 1. 连接池配置
```dart
// lib/core/network/connection_pool.dart
class ConnectionPoolConfig {
  static HttpClient createHttpClient() {
    final client = HttpClient();

    // 连接池配置
    client.maxConnectionsPerHost = 6;
    client.connectionTimeout = NetworkConfig.connectTimeout;
    client.idleTimeout = Duration(seconds: 15);

    // 证书验证
    client.badCertificateCallback = CertificatePinning.badCertificateCallback;

    // 用户代理
    client.userAgent = 'FlutterScaffold/1.0.0 (Mobile App)';

    return client;
  }
}
```

#### 2. 缓存策略配置
```dart
// lib/core/network/cache_config.dart
class CacheConfig {
  // 缓存策略定义
  static const Map<String, Duration> cacheDurations = {
    '/api/user/profile': Duration(minutes: 5),
    '/api/settings': Duration(hours: 1),
    '/api/static': Duration(days: 1),
    '/api/images': Duration(days: 7),
  };

  // 缓存大小限制
  static const int maxCacheSize = 50 * 1024 * 1024; // 50MB
  static const int maxCacheEntries = 1000;

  // 获取缓存策略
  static Duration? getCacheDuration(String path) {
    for (final pattern in cacheDurations.keys) {
      if (path.startsWith(pattern)) {
        return cacheDurations[pattern];
      }
    }
    return null;
  }
}
```

#### 3. 重试策略配置
```dart
// lib/core/network/retry_config.dart
class RetryConfig {
  static const int maxRetries = 3;
  static const Duration initialDelay = Duration(seconds: 1);
  static const double backoffMultiplier = 2.0;

  // 可重试的错误类型
  static const List<DioExceptionType> retryableErrors = [
    DioExceptionType.connectionTimeout,
    DioExceptionType.sendTimeout,
    DioExceptionType.receiveTimeout,
    DioExceptionType.connectionError,
  ];

  // 可重试的状态码
  static const List<int> retryableStatusCodes = [
    408, // Request Timeout
    429, // Too Many Requests
    500, // Internal Server Error
    502, // Bad Gateway
    503, // Service Unavailable
    504, // Gateway Timeout
  ];

  static bool shouldRetry(DioException error) {
    // 检查错误类型
    if (retryableErrors.contains(error.type)) {
      return true;
    }

    // 检查状态码
    final statusCode = error.response?.statusCode;
    if (statusCode != null && retryableStatusCodes.contains(statusCode)) {
      return true;
    }

    return false;
  }

  static Duration getRetryDelay(int attemptNumber) {
    return Duration(
      milliseconds: (initialDelay.inMilliseconds *
                    math.pow(backoffMultiplier, attemptNumber)).round(),
    );
  }
}
```

## 🚨 故障排除指南

### 常见配置问题

#### 1. 依赖冲突解决
```bash
# 清理依赖缓存
flutter clean
flutter pub cache clean

# 重新获取依赖
flutter pub get

# 检查依赖冲突
flutter pub deps
```

#### 2. 代码生成问题
```bash
# 清理生成文件
find . -name "*.g.dart" -delete
find . -name "*.freezed.dart" -delete
find . -name "*.config.dart" -delete

# 重新生成
dart run build_runner build --delete-conflicting-outputs
```

#### 3. 平台配置问题

##### Android 构建失败
```bash
# 清理 Android 构建缓存
cd android
./gradlew clean

# 检查 Gradle 配置
./gradlew dependencies

# 重新构建
cd ..
flutter build apk --debug
```

##### iOS 构建失败
```bash
# 清理 iOS 构建缓存
cd ios
rm -rf build/
rm -rf Pods/
rm Podfile.lock

# 重新安装 Pods
pod install

# 重新构建
cd ..
flutter build ios --debug --no-codesign
```

#### 4. 主题配置问题

##### 主题扩展未生效
```dart
// 确保在 MaterialApp 中正确配置
MaterialApp(
  theme: AppTheme.lightTheme(themeName),
  darkTheme: AppTheme.darkTheme(themeName),
  // 其他配置...
)

// 确保扩展正确注册
ThemeData(
  extensions: [
    CustomColorExtension.light,
    SpacingExtension.standard,
    BorderRadiusExtension.standard,
  ],
)
```

##### 颜色扩展访问失败
```dart
// ❌ 错误用法
final colors = Theme.of(context).extension<CustomColorExtension>();
final successColor = colors.success; // 可能为 null

// ✅ 正确用法
final colors = Theme.of(context).extension<CustomColorExtension>()!;
final successColor = colors.success;

// ✅ 更安全的用法
final colors = Theme.of(context).extension<CustomColorExtension>() ??
               CustomColorExtension.light;
final successColor = colors.success;
```

### 性能优化故障排除

#### 1. 构建性能问题
```bash
# 分析构建时间
flutter build apk --analyze-size

# 检查包大小
flutter build apk --split-per-abi

# 启用 R8 优化
flutter build apk --obfuscate --split-debug-info=build/debug-info
```

#### 2. 运行时性能问题
```dart
// 启用性能监控
import 'package:flutter/rendering.dart';

void main() {
  // 启用性能叠加层
  debugPaintSizeEnabled = false; // 显示组件边界
  debugRepaintRainbowEnabled = false; // 显示重绘区域

  runApp(MyApp());
}
```

#### 3. 内存泄漏检测
```dart
// 使用 WeakReference 避免内存泄漏
class CacheManager {
  final Map<String, WeakReference<Object>> _cache = {};

  void addToCache(String key, Object value) {
    _cache[key] = WeakReference(value);
  }

  Object? getFromCache(String key) {
    return _cache[key]?.target;
  }

  void cleanupCache() {
    _cache.removeWhere((key, ref) => ref.target == null);
  }
}
```

### 网络配置故障排除

#### 1. 证书固定问题
```dart
// 调试证书信息
void debugCertificate(X509Certificate cert) {
  print('Certificate Subject: ${cert.subject}');
  print('Certificate Issuer: ${cert.issuer}');
  print('Certificate SHA256: ${sha256.convert(cert.der)}');
  print('Certificate Valid From: ${cert.startValidity}');
  print('Certificate Valid To: ${cert.endValidity}');
}
```

#### 2. 网络连接问题
```dart
// 网络连接检测
class NetworkConnectivity {
  static Future<bool> isConnected() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    }
  }

  static Future<bool> canReachAPI() async {
    try {
      final dio = Dio();
      final response = await dio.get(
        '${NetworkConfig.baseUrl}/health',
        options: Options(
          connectTimeout: Duration(seconds: 5),
          receiveTimeout: Duration(seconds: 5),
        ),
      );
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }
}
```

#### 3. 缓存问题诊断
```dart
// 缓存状态检查
class CacheDiagnostics {
  static void printCacheStats() {
    final cacheManager = DefaultCacheManager();

    print('Cache Directory: ${cacheManager.store.filePath}');
    print('Cache Size: ${cacheManager.store.}');

    // 清理过期缓存
    cacheManager.emptyCache();
  }
}
```

## 🔧 配置验证工具

### 自动化配置检查

#### 1. 环境配置验证
```dart
// lib/core/utils/config_validator.dart
class ConfigValidator {
  static Future<List<String>> validateEnvironment() async {
    final issues = <String>[];

    // 检查 Flutter 版本
    final flutterVersion = await _getFlutterVersion();
    if (!_isValidFlutterVersion(flutterVersion)) {
      issues.add('Flutter version $flutterVersion is not supported');
    }

    // 检查必需的环境变量
    final requiredEnvVars = ['API_BASE_URL', 'APP_NAME'];
    for (final envVar in requiredEnvVars) {
      if (Platform.environment[envVar] == null) {
        issues.add('Missing environment variable: $envVar');
      }
    }

    // 检查网络连接
    if (!await NetworkConnectivity.isConnected()) {
      issues.add('No internet connection available');
    }

    return issues;
  }

  static Future<String> _getFlutterVersion() async {
    // 实现获取 Flutter 版本的逻辑
    return '3.32.5';
  }

  static bool _isValidFlutterVersion(String version) {
    // 实现版本验证逻辑
    return version.startsWith('3.');
  }
}
```

#### 2. 依赖配置检查
```dart
// lib/core/utils/dependency_checker.dart
class DependencyChecker {
  static Future<Map<String, bool>> checkDependencies() async {
    final results = <String, bool>{};

    // 检查关键依赖
    final criticalDependencies = [
      'flutter_bloc',
      'get_it',
      'dio',
      'go_router',
      'flutter_secure_storage',
    ];

    for (final dep in criticalDependencies) {
      results[dep] = await _isDependencyAvailable(dep);
    }

    return results;
  }

  static Future<bool> _isDependencyAvailable(String packageName) async {
    try {
      // 尝试导入包
      switch (packageName) {
        case 'flutter_bloc':
          return true; // 实际实现中检查包是否可用
        case 'get_it':
          return true;
        // 其他依赖检查...
        default:
          return false;
      }
    } catch (e) {
      return false;
    }
  }
}
```

### 配置文件生成工具

#### 1. 自动生成配置
```dart
// lib/core/utils/config_generator.dart
class ConfigGenerator {
  static Future<void> generateEnvironmentConfig(String environment) async {
    final config = _getConfigForEnvironment(environment);
    final configFile = File('lib/core/config/app_config.dart');

    await configFile.writeAsString(_generateConfigCode(config));
    print('Generated config for $environment environment');
  }

  static Map<String, dynamic> _getConfigForEnvironment(String env) {
    switch (env) {
      case 'development':
        return {
          'apiBaseUrl': 'https://dev-api.example.com',
          'enableLogging': true,
          'enableDebugMode': true,
        };
      case 'staging':
        return {
          'apiBaseUrl': 'https://staging-api.example.com',
          'enableLogging': true,
          'enableDebugMode': false,
        };
      case 'production':
        return {
          'apiBaseUrl': 'https://api.example.com',
          'enableLogging': false,
          'enableDebugMode': false,
        };
      default:
        throw ArgumentError('Unknown environment: $env');
    }
  }

  static String _generateConfigCode(Map<String, dynamic> config) {
    final buffer = StringBuffer();
    buffer.writeln('// Generated configuration file');
    buffer.writeln('// Do not edit manually');
    buffer.writeln();
    buffer.writeln('class AppConfig {');

    config.forEach((key, value) {
      final type = value.runtimeType.toString();
      buffer.writeln('  static const $type $key = $value;');
    });

    buffer.writeln('}');
    return buffer.toString();
  }
}
```

## 📚 相关文档

### 核心技术文档
- [Clean Architecture 指南](CLEAN_ARCHITECTURE_GUIDE_PRO.md)
- [状态管理指南](STATE_MANAGEMENT_GUIDE.md)
- [UI 设计指南](UI_GUIDE_PRO.md)
- [后端 API 指南](BACKEND_API_GUIDE.md)
- [Dart 开发指南](DART_GUIDE_PRO.md)

### 开发工作流
- [开发工作流完整指南](DEVELOPMENT_WORKFLOW_GUIDE.md)

### 项目管理
- [项目总结报告](FINAL_SUMMARY_REPORT.md)
- [架构决策记录](adr/)

## 📖 配置快速参考

### 常用配置命令
```bash
# 项目设置 (推荐使用make)
make setup                    # 初始化开发环境
mise run setup               # 或使用: mise r s
make bootstrap               # 安装所有依赖
mise run bootstrap           # 或使用: mise r bs

# 代码生成
make gen                     # 生成所有代码
mise run gen                 # 或使用: mise r g
dart run build_runner watch # 监听模式生成

# 构建
make build-prod             # Android 生产环境构建
mise run build-prod         # 或使用: mise r bp
make build-ios-prod         # iOS 生产环境构建
mise run build-ios-prod     # 或使用: mise r bip
flutter build web --release # Web 发布构建

# 分析和测试
make analyze                # 代码分析
mise run analyze            # 或使用: mise r a
make test                   # 运行测试
mise run test               # 或使用: mise r t
make format                 # 代码格式化
mise run format             # 或使用: mise r f
```

### 重要配置文件位置
```
项目根目录/
├── melos.yaml              # Monorepo 配置
├── Makefile               # 构建命令
├── .cspell.json           # 拼写检查
└── apps/mobile/
    ├── pubspec.yaml       # 依赖配置
    ├── build.yaml         # 代码生成配置
    ├── analysis_options.yaml  # 静态分析
    ├── .metadata          # Flutter 元数据
    ├── android/app/build.gradle  # Android 配置
    └── ios/Runner/Info.plist     # iOS 配置
```

### 环境变量配置
```bash
# 开发环境
export FLUTTER_ENV=development
export API_BASE_URL=https://dev-api.example.com
export ENABLE_LOGGING=true

# 生产环境
export FLUTTER_ENV=production
export API_BASE_URL=https://api.example.com
export ENABLE_LOGGING=false
```

# 🚀 Flutter 第三方支付集成开发指南

## 📋 项目概述

本指南基于Flutter Scaffold项目架构，采用渐进式集成策略，实现微信登录、微信支付和支付宝支付功能。

### 🎯 技术栈选择
- **微信SDK**: `fluwx ^5.7.1` (最新稳定版)
- **支付宝SDK**: `tobias ^5.1.2` (企业级稳定版)
- **Google/Apple Pay**: `pay ^2.0.0` (官方支持，补充方案)

### 🏗️ 集成策略
采用方案A渐进式集成，分三个阶段实施：
1. **阶段一**: 微信登录集成
2. **阶段二**: 微信支付集成
3. **阶段三**: 支付宝支付集成

---

## 🔧 阶段一：微信登录集成

### 1.1 依赖配置

#### pubspec.yaml 配置
```yaml
dependencies:
  fluwx: ^5.7.1

# 微信配置
fluwx:
  app_id: "your_wechat_app_id"
  universal_link: "https://your-domain.com/wechat/"
  debug_logging: true
```

#### Android 配置要点
- **签名配置**: 必须与微信开放平台注册的签名一致
- **包名配置**: 确保与微信开放平台注册的包名匹配
- **ProGuard规则**: 添加微信SDK混淆规则

#### iOS 配置要点
- **Universal Link**: 配置域名验证文件
- **URL Scheme**: 设置微信回调scheme
- **Info.plist**: 添加微信查询白名单

### 1.2 架构集成

#### 依赖注入配置
```dart
// lib/core/di/payment_module.dart
@module
abstract class PaymentModule {
  @lazySingleton
  WeChatAuthService provideWeChatAuthService() => WeChatAuthService();

  @lazySingleton
  PaymentRepository providePaymentRepository(
    WeChatAuthService weChatService,
    ApiClient apiClient,
  ) => PaymentRepositoryImpl(weChatService, apiClient);
}
```

#### BLoC状态管理
```dart
// lib/features/auth/presentation/bloc/wechat_auth_bloc.dart
class WeChatAuthBloc extends Bloc<WeChatAuthEvent, WeChatAuthState> {
  final WeChatAuthUseCase _authUseCase;

  WeChatAuthBloc(this._authUseCase) : super(WeChatAuthInitial()) {
    on<WeChatLoginRequested>(_onLoginRequested);
    on<WeChatAuthResultReceived>(_onAuthResultReceived);
  }
}
```

### 1.3 后端接口需求

#### 接口1: 微信登录验证
```http
POST /api/auth/wechat/login
Content-Type: application/json

{
  "code": "微信授权码",
  "state": "状态参数"
}

Response:
{
  "success": true,
  "data": {
    "access_token": "用户访问令牌",
    "refresh_token": "刷新令牌",
    "user_info": {
      "id": "用户ID",
      "nickname": "用户昵称",
      "avatar": "头像URL"
    }
  }
}
```

#### 接口2: 用户信息同步
```http
POST /api/auth/wechat/sync-user
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "wechat_user_info": {
    "openid": "微信OpenID",
    "unionid": "微信UnionID",
    "nickname": "用户昵称",
    "avatar": "头像URL"
  }
}
```

### 1.4 开发节点检查清单

- [ ] **开发环境准备**
  - [ ] 微信开放平台账号申请
  - [ ] 应用注册和审核
  - [ ] 获取AppID和AppSecret

- [ ] **前端开发**
  - [ ] 依赖集成和配置
  - [ ] 登录UI组件开发
  - [ ] 状态管理实现
  - [ ] 错误处理机制

- [ ] **后端开发** (需要后端团队配合)
  - [ ] 微信登录验证接口
  - [ ] 用户信息同步接口
  - [ ] 令牌管理机制

- [ ] **平台配置**
  - [ ] Android签名配置
  - [ ] iOS Universal Link配置
  - [ ] 微信开放平台回调URL设置

---

## 🔧 阶段二：微信支付集成

### 2.1 支付架构设计

#### 支付服务抽象
```dart
// lib/core/payment/payment_service.dart
abstract class PaymentService {
  Future<PaymentResult> processPayment(PaymentRequest request);
  Future<bool> isPaymentAvailable();
  String get paymentMethod;
}

class WeChatPaymentService implements PaymentService {
  final Fluwx _fluwx;
  final PaymentRepository _repository;

  @override
  Future<PaymentResult> processPayment(PaymentRequest request) async {
    // 1. 向后端请求支付参数
    // 2. 调用微信支付SDK
    // 3. 处理支付结果
  }
}
```

#### 支付状态管理
```dart
// lib/features/payment/presentation/bloc/payment_bloc.dart
class PaymentBloc extends Bloc<PaymentEvent, PaymentState> {
  final List<PaymentService> _paymentServices;

  PaymentBloc(this._paymentServices) : super(PaymentInitial()) {
    on<PaymentRequested>(_onPaymentRequested);
    on<PaymentResultReceived>(_onPaymentResultReceived);
    on<PaymentMethodSelected>(_onPaymentMethodSelected);
  }
}
```

### 2.2 后端接口需求

#### 接口3: 创建支付订单
```http
POST /api/payment/create-order
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "amount": 100,
  "currency": "CNY",
  "description": "商品描述",
  "payment_method": "wechat",
  "extra_data": {
    "product_id": "商品ID",
    "user_id": "用户ID"
  }
}

Response:
{
  "success": true,
  "data": {
    "order_id": "订单ID",
    "payment_params": {
      "appid": "微信AppID",
      "partnerid": "商户号",
      "prepayid": "预支付ID",
      "package": "Sign=WXPay",
      "noncestr": "随机字符串",
      "timestamp": "时间戳",
      "sign": "签名"
    }
  }
}
```

#### 接口4: 支付结果验证
```http
POST /api/payment/verify-result
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "order_id": "订单ID",
  "payment_result": {
    "return_code": "SUCCESS",
    "result_code": "SUCCESS",
    "transaction_id": "微信交易号"
  }
}

Response:
{
  "success": true,
  "data": {
    "order_status": "paid",
    "payment_time": "2024-01-01T12:00:00Z"
  }
}
```

### 2.3 开发节点检查清单

- [ ] **商户资质准备**
  - [ ] 微信商户平台账号
  - [ ] 商户号申请和认证
  - [ ] 支付权限开通

- [ ] **前端开发**
  - [ ] 支付UI组件开发
  - [ ] 支付流程状态管理
  - [ ] 支付结果处理
  - [ ] 异常情况处理

- [ ] **后端开发** (需要后端团队配合)
  - [ ] 支付订单创建接口
  - [ ] 微信支付参数生成
  - [ ] 支付结果验证接口
  - [ ] 支付回调处理

- [ ] **安全配置**
  - [ ] 支付密钥配置
  - [ ] 签名验证机制
  - [ ] 订单防重复提交

---

## 🔧 阶段三：支付宝支付集成

### 3.1 支付宝集成配置

#### pubspec.yaml 配置
```yaml
dependencies:
  tobias: ^5.1.2

# 支付宝配置
tobias:
  url_scheme: "your_app_scheme"
```

#### 支付宝服务实现
```dart
// lib/core/payment/alipay_service.dart
class AliPayService implements PaymentService {
  final Tobias _tobias;
  final PaymentRepository _repository;

  @override
  Future<PaymentResult> processPayment(PaymentRequest request) async {
    // 1. 检查支付宝是否安装
    // 2. 向后端请求支付订单字符串
    // 3. 调用支付宝SDK
    // 4. 处理支付结果
  }

  @override
  Future<bool> isPaymentAvailable() async {
    return await _tobias.isAliPayInstalled;
  }
}
```

### 3.2 后端接口需求

#### 接口5: 支付宝订单创建
```http
POST /api/payment/alipay/create-order
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "amount": 100,
  "subject": "商品标题",
  "body": "商品描述",
  "out_trade_no": "商户订单号"
}

Response:
{
  "success": true,
  "data": {
    "order_string": "支付宝订单字符串",
    "order_id": "内部订单ID"
  }
}
```

### 3.3 开发节点检查清单

- [ ] **支付宝商户准备**
  - [ ] 支付宝开放平台账号
  - [ ] 应用创建和配置
  - [ ] 支付功能申请

- [ ] **前端开发**
  - [ ] 支付宝支付集成
  - [ ] 多支付方式选择UI
  - [ ] 统一支付结果处理

- [ ] **后端开发** (需要后端团队配合)
  - [ ] 支付宝订单创建接口
  - [ ] 支付宝异步通知处理
  - [ ] 订单状态同步机制

---

## 🔄 统一支付管理器

### 支付方式自动选择
```dart
// lib/core/payment/payment_manager.dart
class PaymentManager {
  final List<PaymentService> _services;

  Future<List<PaymentMethod>> getAvailablePaymentMethods() async {
    final available = <PaymentMethod>[];
    for (final service in _services) {
      if (await service.isPaymentAvailable()) {
        available.add(PaymentMethod.fromService(service));
      }
    }
    return available;
  }

  Future<PaymentResult> processPayment(
    PaymentRequest request,
    PaymentMethod method,
  ) async {
    final service = _getServiceByMethod(method);
    return await service.processPayment(request);
  }
}
```

## 🛡️ 安全最佳实践

### 1. 支付安全
- 所有支付参数由服务端生成
- 客户端仅处理支付调用和结果回调
- 支付结果必须经过服务端验证

### 2. 数据安全
- 敏感信息使用`flutter_secure_storage`存储
- 网络传输使用HTTPS加密
- 支付密钥服务端管理

### 3. 用户体验
- 支付过程提供明确的状态反馈
- 网络异常时提供重试机制
- 支付失败时提供友好的错误提示

---

## 📱 开发时序图

```
用户发起支付 → 前端创建订单请求 → 后端生成支付参数 →
前端调用支付SDK → 第三方支付处理 → 支付结果回调 →
前端验证结果 → 后端确认支付状态 → 更新订单状态
```

## 🔗 关键开发节点

### 需要后端配合的节点
1. **微信登录验证接口** - 阶段一必需
2. **支付订单创建接口** - 阶段二必需
3. **支付结果验证接口** - 阶段二必需
4. **支付宝订单接口** - 阶段三必需
5. **异步支付通知处理** - 各阶段必需

### 前端独立开发节点
1. UI组件和交互逻辑
2. 状态管理和错误处理
3. 本地数据缓存和安全存储
4. 支付方式检测和选择逻辑

---

## 📝 详细代码实现

### 微信登录完整实现

#### Domain层 - 用例定义
```dart
// lib/features/auth/domain/usecases/wechat_auth_usecase.dart
@injectable
class WeChatAuthUseCase {
  final AuthRepository _repository;

  WeChatAuthUseCase(this._repository);

  Future<Either<Failure, AuthResult>> login() async {
    try {
      // 1. 检查微信是否安装
      final isInstalled = await Fluwx().isWeChatInstalled;
      if (!isInstalled) {
        return Left(WeChatNotInstalledFailure());
      }

      // 2. 发起微信授权
      final authResult = await Fluwx().sendWeChatAuth(
        scope: "snsapi_userinfo",
        state: "flutter_scaffold_auth",
      );

      if (authResult.isSuccessful) {
        // 3. 向后端验证授权码
        return await _repository.loginWithWeChat(authResult.code!);
      } else {
        return Left(WeChatAuthFailure(authResult.errorCode));
      }
    } catch (e) {
      return Left(UnknownFailure(e.toString()));
    }
  }
}
```

#### Data层 - Repository实现
```dart
// lib/features/auth/data/repositories/auth_repository_impl.dart
@LazySingleton(as: AuthRepository)
class AuthRepositoryImpl implements AuthRepository {
  final ApiClient _apiClient;
  final TokenManager _tokenManager;

  AuthRepositoryImpl(this._apiClient, this._tokenManager);

  @override
  Future<Either<Failure, AuthResult>> loginWithWeChat(String code) async {
    try {
      final response = await _apiClient.wechatLogin(
        WeChatLoginRequest(code: code, state: "flutter_scaffold_auth"),
      );

      // 保存令牌
      await _tokenManager.saveTokens(
        accessToken: response.data.accessToken,
        refreshToken: response.data.refreshToken,
      );

      return Right(AuthResult(
        user: response.data.userInfo,
        accessToken: response.data.accessToken,
      ));
    } on DioException catch (e) {
      return Left(NetworkFailure(e.message ?? 'Network error'));
    } catch (e) {
      return Left(UnknownFailure(e.toString()));
    }
  }
}
```

#### Presentation层 - BLoC实现
```dart
// lib/features/auth/presentation/bloc/wechat_auth_bloc.dart
@injectable
class WeChatAuthBloc extends Bloc<WeChatAuthEvent, WeChatAuthState> {
  final WeChatAuthUseCase _authUseCase;

  WeChatAuthBloc(this._authUseCase) : super(WeChatAuthInitial()) {
    on<WeChatLoginRequested>(_onLoginRequested);
  }

  Future<void> _onLoginRequested(
    WeChatLoginRequested event,
    Emitter<WeChatAuthState> emit,
  ) async {
    emit(WeChatAuthLoading());

    final result = await _authUseCase.login();

    result.fold(
      (failure) => emit(WeChatAuthFailure(failure.message)),
      (authResult) => emit(WeChatAuthSuccess(authResult)),
    );
  }
}
```

### 支付服务完整实现

#### 统一支付接口
```dart
// lib/core/payment/domain/payment_service.dart
abstract class PaymentService {
  String get paymentMethod;
  Future<bool> isAvailable();
  Future<PaymentResult> pay(PaymentRequest request);
}

@freezed
class PaymentRequest with _$PaymentRequest {
  const factory PaymentRequest({
    required String orderId,
    required double amount,
    required String currency,
    required String description,
    Map<String, dynamic>? extraData,
  }) = _PaymentRequest;
}

@freezed
class PaymentResult with _$PaymentResult {
  const factory PaymentResult({
    required bool isSuccess,
    required String orderId,
    String? transactionId,
    String? errorCode,
    String? errorMessage,
    Map<String, dynamic>? extraData,
  }) = _PaymentResult;
}
```

#### 微信支付服务实现
```dart
// lib/core/payment/services/wechat_payment_service.dart
@LazySingleton(as: PaymentService)
class WeChatPaymentService implements PaymentService {
  final Fluwx _fluwx;
  final PaymentRepository _repository;

  WeChatPaymentService(this._fluwx, this._repository);

  @override
  String get paymentMethod => 'wechat';

  @override
  Future<bool> isAvailable() async {
    return await _fluwx.isWeChatInstalled;
  }

  @override
  Future<PaymentResult> pay(PaymentRequest request) async {
    try {
      // 1. 向后端请求支付参数
      final orderResult = await _repository.createWeChatOrder(request);

      if (orderResult.isLeft()) {
        return PaymentResult(
          isSuccess: false,
          orderId: request.orderId,
          errorMessage: 'Failed to create order',
        );
      }

      final paymentParams = orderResult.getOrElse(() => throw Exception());

      // 2. 调用微信支付
      final payResult = await _fluwx.pay(
        appId: paymentParams.appId,
        partnerId: paymentParams.partnerId,
        prepayId: paymentParams.prepayId,
        packageValue: paymentParams.package,
        nonceStr: paymentParams.nonceStr,
        timeStamp: paymentParams.timestamp,
        sign: paymentParams.sign,
      );

      // 3. 处理支付结果
      if (payResult.isSuccessful) {
        // 向后端验证支付结果
        await _repository.verifyPaymentResult(
          request.orderId,
          payResult.toMap(),
        );

        return PaymentResult(
          isSuccess: true,
          orderId: request.orderId,
          transactionId: payResult.transactionId,
        );
      } else {
        return PaymentResult(
          isSuccess: false,
          orderId: request.orderId,
          errorCode: payResult.errorCode.toString(),
          errorMessage: payResult.errorMessage,
        );
      }
    } catch (e) {
      return PaymentResult(
        isSuccess: false,
        orderId: request.orderId,
        errorMessage: e.toString(),
      );
    }
  }
}
```

#### 支付宝支付服务实现
```dart
// lib/core/payment/services/alipay_service.dart
@LazySingleton(as: PaymentService)
class AliPayService implements PaymentService {
  final Tobias _tobias;
  final PaymentRepository _repository;

  AliPayService(this._tobias, this._repository);

  @override
  String get paymentMethod => 'alipay';

  @override
  Future<bool> isAvailable() async {
    return await _tobias.isAliPayInstalled;
  }

  @override
  Future<PaymentResult> pay(PaymentRequest request) async {
    try {
      // 1. 向后端请求支付订单字符串
      final orderResult = await _repository.createAliPayOrder(request);

      if (orderResult.isLeft()) {
        return PaymentResult(
          isSuccess: false,
          orderId: request.orderId,
          errorMessage: 'Failed to create order',
        );
      }

      final orderString = orderResult.getOrElse(() => throw Exception());

      // 2. 调用支付宝支付
      final payResult = await _tobias.pay(orderString);

      // 3. 解析支付结果
      final resultMap = Map<String, dynamic>.from(payResult);
      final resultStatus = resultMap['resultStatus'] as String?;

      if (resultStatus == '9000') {
        // 支付成功，向后端验证
        await _repository.verifyPaymentResult(
          request.orderId,
          resultMap,
        );

        return PaymentResult(
          isSuccess: true,
          orderId: request.orderId,
          extraData: resultMap,
        );
      } else {
        return PaymentResult(
          isSuccess: false,
          orderId: request.orderId,
          errorCode: resultStatus,
          errorMessage: resultMap['memo'] as String?,
        );
      }
    } catch (e) {
      return PaymentResult(
        isSuccess: false,
        orderId: request.orderId,
        errorMessage: e.toString(),
      );
    }
  }
}
```

### API客户端扩展

#### 支付相关API定义
```dart
// lib/core/network/api_client.dart (扩展现有的ApiClient)
@RestApi()
abstract class ApiClient {
  // 现有方法...

  // 微信登录
  @POST('/api/auth/wechat/login')
  Future<ApiResponse<WeChatLoginResponse>> wechatLogin(
    @Body() WeChatLoginRequest request,
  );

  // 创建微信支付订单
  @POST('/api/payment/wechat/create-order')
  Future<ApiResponse<WeChatPaymentParams>> createWeChatOrder(
    @Body() CreateOrderRequest request,
  );

  // 创建支付宝订单
  @POST('/api/payment/alipay/create-order')
  Future<ApiResponse<AliPayOrderResponse>> createAliPayOrder(
    @Body() CreateOrderRequest request,
  );

  // 验证支付结果
  @POST('/api/payment/verify-result')
  Future<ApiResponse<PaymentVerificationResponse>> verifyPaymentResult(
    @Body() PaymentVerificationRequest request,
  );
}
```

#### 数据模型定义
```dart
// lib/features/payment/data/models/payment_models.dart
@freezed
class WeChatLoginRequest with _$WeChatLoginRequest {
  const factory WeChatLoginRequest({
    required String code,
    required String state,
  }) = _WeChatLoginRequest;

  factory WeChatLoginRequest.fromJson(Map<String, dynamic> json) =>
      _$WeChatLoginRequestFromJson(json);
}

@freezed
class WeChatLoginResponse with _$WeChatLoginResponse {
  const factory WeChatLoginResponse({
    required String accessToken,
    required String refreshToken,
    required UserInfo userInfo,
  }) = _WeChatLoginResponse;

  factory WeChatLoginResponse.fromJson(Map<String, dynamic> json) =>
      _$WeChatLoginResponseFromJson(json);
}

@freezed
class CreateOrderRequest with _$CreateOrderRequest {
  const factory CreateOrderRequest({
    required double amount,
    required String currency,
    required String description,
    required String paymentMethod,
    Map<String, dynamic>? extraData,
  }) = _CreateOrderRequest;

  factory CreateOrderRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateOrderRequestFromJson(json);
}

@freezed
class WeChatPaymentParams with _$WeChatPaymentParams {
  const factory WeChatPaymentParams({
    required String appId,
    required String partnerId,
    required String prepayId,
    required String package,
    required String nonceStr,
    required String timestamp,
    required String sign,
  }) = _WeChatPaymentParams;

  factory WeChatPaymentParams.fromJson(Map<String, dynamic> json) =>
      _$WeChatPaymentParamsFromJson(json);
}
```

---

## 🔧 平台特定配置

### Android配置详细步骤

#### 1. build.gradle配置
```gradle
// android/app/build.gradle
android {
    defaultConfig {
        // 确保包名与微信/支付宝注册的一致
        applicationId "com.example.flutter_scaffold_mobile"
    }

    signingConfigs {
        release {
            // 配置发布签名，必须与第三方平台注册的一致
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }
}
```

#### 2. AndroidManifest.xml配置
```xml
<!-- android/app/src/main/AndroidManifest.xml -->
<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />

    <application>
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop">

            <!-- 微信回调 -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="your_wechat_app_id" />
            </intent-filter>

            <!-- 支付宝回调 -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="your_app_scheme" />
            </intent-filter>
        </activity>
    </application>

    <!-- 查询微信和支付宝 -->
    <queries>
        <package android:name="com.tencent.mm" />
        <package android:name="com.eg.android.AlipayGphone" />
    </queries>
</manifest>
```

### iOS配置详细步骤

#### 1. Info.plist配置
```xml
<!-- ios/Runner/Info.plist -->
<dict>
    <!-- URL Schemes -->
    <key>CFBundleURLTypes</key>
    <array>
        <dict>
            <key>CFBundleURLName</key>
            <string>wechat</string>
            <key>CFBundleURLSchemes</key>
            <array>
                <string>your_wechat_app_id</string>
            </array>
        </dict>
        <dict>
            <key>CFBundleURLName</key>
            <string>alipay</string>
            <key>CFBundleURLSchemes</key>
            <array>
                <string>your_app_scheme</string>
            </array>
        </dict>
    </array>

    <!-- 查询白名单 -->
    <key>LSApplicationQueriesSchemes</key>
    <array>
        <string>weixin</string>
        <string>weixinULAPI</string>
        <string>alipay</string>
        <string>alipays</string>
    </array>

    <!-- Associated Domains (Universal Link) -->
    <key>com.apple.developer.associated-domains</key>
    <array>
        <string>applinks:your-domain.com</string>
    </array>
</dict>
```

#### 2. Universal Link配置
在您的域名根目录下创建 `.well-known/apple-app-site-association` 文件：
```json
{
    "applinks": {
        "apps": [],
        "details": [
            {
                "appID": "TEAM_ID.com.example.flutter_scaffold_mobile",
                "paths": ["/wechat/*"]
            }
        ]
    }
}
```

---

## 🧪 测试策略

### 单元测试示例
```dart
// test/features/payment/wechat_payment_service_test.dart
void main() {
  group('WeChatPaymentService', () {
    late WeChatPaymentService service;
    late MockFluwx mockFluwx;
    late MockPaymentRepository mockRepository;

    setUp(() {
      mockFluwx = MockFluwx();
      mockRepository = MockPaymentRepository();
      service = WeChatPaymentService(mockFluwx, mockRepository);
    });

    test('should return true when WeChat is installed', () async {
      // Arrange
      when(() => mockFluwx.isWeChatInstalled).thenAnswer((_) async => true);

      // Act
      final result = await service.isAvailable();

      // Assert
      expect(result, true);
    });

    test('should process payment successfully', () async {
      // Arrange
      final request = PaymentRequest(
        orderId: 'test_order_123',
        amount: 100.0,
        currency: 'CNY',
        description: 'Test payment',
      );

      when(() => mockRepository.createWeChatOrder(any()))
          .thenAnswer((_) async => Right(mockPaymentParams));
      when(() => mockFluwx.pay(any()))
          .thenAnswer((_) async => mockSuccessPayResult);

      // Act
      final result = await service.pay(request);

      // Assert
      expect(result.isSuccess, true);
      expect(result.orderId, 'test_order_123');
    });
  });
}
```

### 集成测试示例
```dart
// integration_test/payment_flow_test.dart
void main() {
  group('Payment Flow Integration Test', () {
    testWidgets('complete WeChat payment flow', (tester) async {
      // 1. 启动应用
      await tester.pumpWidget(MyApp());

      // 2. 导航到支付页面
      await tester.tap(find.byKey(Key('payment_button')));
      await tester.pumpAndSettle();

      // 3. 选择微信支付
      await tester.tap(find.byKey(Key('wechat_pay_option')));
      await tester.pumpAndSettle();

      // 4. 确认支付
      await tester.tap(find.byKey(Key('confirm_payment')));
      await tester.pumpAndSettle();

      // 5. 验证支付结果页面
      expect(find.text('支付成功'), findsOneWidget);
    });
  });
}
```

---

## 🚨 错误处理和异常管理

### 统一错误处理机制
```dart
// lib/core/payment/domain/payment_failures.dart
@freezed
class PaymentFailure with _$PaymentFailure implements Failure {
  const factory PaymentFailure.networkError(String message) = NetworkPaymentFailure;
  const factory PaymentFailure.wechatNotInstalled() = WeChatNotInstalledFailure;
  const factory PaymentFailure.alipayNotInstalled() = AliPayNotInstalledFailure;
  const factory PaymentFailure.userCancelled() = UserCancelledFailure;
  const factory PaymentFailure.paymentFailed(String errorCode, String message) = PaymentProcessFailure;
  const factory PaymentFailure.orderCreationFailed(String message) = OrderCreationFailure;
  const factory PaymentFailure.verificationFailed(String message) = VerificationFailure;
  const factory PaymentFailure.unknown(String message) = UnknownPaymentFailure;
}

// 错误处理扩展
extension PaymentFailureX on PaymentFailure {
  String get userFriendlyMessage {
    return when(
      networkError: (message) => '网络连接异常，请检查网络后重试',
      wechatNotInstalled: () => '请先安装微信客户端',
      alipayNotInstalled: () => '请先安装支付宝客户端',
      userCancelled: () => '支付已取消',
      paymentFailed: (code, message) => '支付失败：$message',
      orderCreationFailed: (message) => '订单创建失败，请重试',
      verificationFailed: (message) => '支付验证失败，请联系客服',
      unknown: (message) => '未知错误，请重试或联系客服',
    );
  }

  bool get isRetryable {
    return when(
      networkError: (_) => true,
      wechatNotInstalled: () => false,
      alipayNotInstalled: () => false,
      userCancelled: () => false,
      paymentFailed: (_, __) => false,
      orderCreationFailed: (_) => true,
      verificationFailed: (_) => false,
      unknown: (_) => true,
    );
  }
}
```

### 支付状态管理增强
```dart
// lib/features/payment/presentation/bloc/payment_state.dart
@freezed
class PaymentState with _$PaymentState {
  const factory PaymentState.initial() = PaymentInitial;
  const factory PaymentState.loading() = PaymentLoading;
  const factory PaymentState.methodsLoaded(List<PaymentMethod> methods) = PaymentMethodsLoaded;
  const factory PaymentState.processing(String orderId) = PaymentProcessing;
  const factory PaymentState.success(PaymentResult result) = PaymentSuccess;
  const factory PaymentState.failure(PaymentFailure failure) = PaymentFailure;
  const factory PaymentState.retryable(PaymentFailure failure, VoidCallback retryAction) = PaymentRetryable;
}

// 支付事件定义
@freezed
class PaymentEvent with _$PaymentEvent {
  const factory PaymentEvent.loadPaymentMethods() = LoadPaymentMethods;
  const factory PaymentEvent.selectPaymentMethod(PaymentMethod method) = SelectPaymentMethod;
  const factory PaymentEvent.processPayment(PaymentRequest request) = ProcessPayment;
  const factory PaymentEvent.retryPayment() = RetryPayment;
  const factory PaymentEvent.cancelPayment() = CancelPayment;
  const factory PaymentEvent.verifyPaymentResult(String orderId) = VerifyPaymentResult;
}
```

### 网络重试机制
```dart
// lib/core/payment/services/payment_retry_service.dart
@injectable
class PaymentRetryService {
  static const int maxRetries = 3;
  static const Duration retryDelay = Duration(seconds: 2);

  Future<T> executeWithRetry<T>(
    Future<T> Function() operation,
    bool Function(dynamic error) shouldRetry,
  ) async {
    int attempts = 0;

    while (attempts < maxRetries) {
      try {
        return await operation();
      } catch (error) {
        attempts++;

        if (attempts >= maxRetries || !shouldRetry(error)) {
          rethrow;
        }

        await Future.delayed(retryDelay * attempts);
      }
    }

    throw Exception('Max retries exceeded');
  }
}
```

---

## 🎨 UI组件实现

### 支付方式选择组件
```dart
// lib/features/payment/presentation/widgets/payment_method_selector.dart
class PaymentMethodSelector extends StatelessWidget {
  final List<PaymentMethod> methods;
  final PaymentMethod? selectedMethod;
  final ValueChanged<PaymentMethod> onMethodSelected;

  const PaymentMethodSelector({
    Key? key,
    required this.methods,
    this.selectedMethod,
    required this.onMethodSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '选择支付方式',
          style: context.textTheme.titleMedium,
        ),
        const SizedBox(height: 16),
        ...methods.map((method) => PaymentMethodTile(
          method: method,
          isSelected: method == selectedMethod,
          onTap: () => onMethodSelected(method),
        )),
      ],
    );
  }
}

class PaymentMethodTile extends StatelessWidget {
  final PaymentMethod method;
  final bool isSelected;
  final VoidCallback onTap;

  const PaymentMethodTile({
    Key? key,
    required this.method,
    required this.isSelected,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: _buildMethodIcon(),
        title: Text(method.displayName),
        subtitle: method.description != null ? Text(method.description!) : null,
        trailing: isSelected
          ? Icon(Icons.check_circle, color: context.colorScheme.primary)
          : const Icon(Icons.radio_button_unchecked),
        onTap: onTap,
      ),
    );
  }

  Widget _buildMethodIcon() {
    switch (method.type) {
      case PaymentMethodType.wechat:
        return const Icon(Icons.wechat, color: Colors.green);
      case PaymentMethodType.alipay:
        return const Icon(Icons.account_balance, color: Colors.blue);
      case PaymentMethodType.applePay:
        return const Icon(Icons.apple, color: Colors.black);
      case PaymentMethodType.googlePay:
        return const Icon(Icons.google, color: Colors.red);
    }
  }
}
```

### 支付结果页面
```dart
// lib/features/payment/presentation/pages/payment_result_page.dart
class PaymentResultPage extends StatelessWidget {
  final PaymentResult result;

  const PaymentResultPage({Key? key, required this.result}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('支付结果'),
        automaticallyImplyLeading: false,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildResultIcon(),
            const SizedBox(height: 24),
            Text(
              result.isSuccess ? '支付成功' : '支付失败',
              style: context.textTheme.headlineMedium?.copyWith(
                color: result.isSuccess ? Colors.green : Colors.red,
              ),
            ),
            const SizedBox(height: 16),
            if (result.transactionId != null) ...[
              Text('交易号：${result.transactionId}'),
              const SizedBox(height: 8),
            ],
            Text('订单号：${result.orderId}'),
            if (!result.isSuccess && result.errorMessage != null) ...[
              const SizedBox(height: 16),
              Text(
                result.errorMessage!,
                style: context.textTheme.bodyMedium?.copyWith(color: Colors.red),
                textAlign: TextAlign.center,
              ),
            ],
            const SizedBox(height: 32),
            Row(
              children: [
                if (!result.isSuccess)
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => context.pop(),
                      child: const Text('重新支付'),
                    ),
                  ),
                if (!result.isSuccess) const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => context.go('/home'),
                    child: const Text('返回首页'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultIcon() {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: result.isSuccess ? Colors.green.withAlpha(51) : Colors.red.withAlpha(51),
      ),
      child: Icon(
        result.isSuccess ? Icons.check : Icons.close,
        size: 48,
        color: result.isSuccess ? Colors.green : Colors.red,
      ),
    );
  }
}
```

---

## 🔍 故障排除指南

### 常见问题及解决方案

#### 1. 微信登录/支付问题
```markdown
**问题**: 微信登录返回错误码 -1
**原因**: 应用签名不匹配
**解决方案**:
1. 检查Android签名是否与微信开放平台注册的一致
2. 确认包名是否正确
3. 验证iOS Bundle ID和Team ID配置

**问题**: 微信支付调用失败
**原因**: 支付参数错误或商户配置问题
**解决方案**:
1. 检查商户号和API密钥配置
2. 验证支付参数签名算法
3. 确认商户支付权限已开通
```

#### 2. 支付宝支付问题
```markdown
**问题**: 支付宝返回状态码 6001
**原因**: 用户取消支付
**解决方案**: 正常业务逻辑，提供重新支付选项

**问题**: 支付宝返回状态码 4000
**原因**: 订单支付失败
**解决方案**:
1. 检查订单参数格式
2. 验证商户配置
3. 确认支付金额和商品信息
```

#### 3. 网络相关问题
```markdown
**问题**: 网络请求超时
**解决方案**:
1. 增加重试机制
2. 优化网络配置
3. 提供离线提示

**问题**: SSL证书验证失败
**解决方案**:
1. 更新证书配置
2. 检查域名配置
3. 验证网络环境
```

### 调试工具和日志

#### 支付调试助手
```dart
// lib/core/payment/debug/payment_debugger.dart
@injectable
class PaymentDebugger {
  final Logger _logger;

  PaymentDebugger(this._logger);

  void logPaymentAttempt(PaymentRequest request) {
    _logger.i('Payment attempt: ${request.toJson()}');
  }

  void logPaymentResult(PaymentResult result) {
    _logger.i('Payment result: ${result.toJson()}');
  }

  void logPaymentError(PaymentFailure failure) {
    _logger.e('Payment error: ${failure.toString()}');
  }

  void logNetworkRequest(String url, Map<String, dynamic> data) {
    _logger.d('Network request to $url: $data');
  }

  void logNetworkResponse(String url, dynamic response) {
    _logger.d('Network response from $url: $response');
  }
}
```

#### 开发环境配置
```dart
// lib/core/config/payment_config.dart
class PaymentConfig {
  static const bool isDebugMode = kDebugMode;
  static const bool enablePaymentLogs = true;
  static const bool enableMockPayments = false; // 仅开发环境

  // 开发环境使用测试参数
  static String get wechatAppId => isDebugMode
    ? 'wx_test_app_id'
    : 'wx_production_app_id';

  static String get alipayScheme => isDebugMode
    ? 'test_app_scheme'
    : 'production_app_scheme';
}
```

---

## 📋 部署检查清单

### 发布前验证
- [ ] **微信配置验证**
  - [ ] 生产环境AppID配置正确
  - [ ] Universal Link域名验证通过
  - [ ] Android发布签名与注册签名一致
  - [ ] iOS Bundle ID与注册信息匹配

- [ ] **支付宝配置验证**
  - [ ] 商户号和应用ID配置正确
  - [ ] 支付宝公钥和私钥配置正确
  - [ ] 回调URL配置正确

- [ ] **安全检查**
  - [ ] 敏感信息不在客户端硬编码
  - [ ] 网络传输使用HTTPS
  - [ ] 支付参数服务端生成和验证
  - [ ] 用户输入数据验证和过滤

- [ ] **功能测试**
  - [ ] 各支付方式正常流程测试
  - [ ] 异常情况处理测试
  - [ ] 网络异常恢复测试
  - [ ] 用户取消支付测试

### 监控和分析
```dart
// lib/core/analytics/payment_analytics.dart
@injectable
class PaymentAnalytics {
  final AnalyticsService _analytics;

  PaymentAnalytics(this._analytics);

  void trackPaymentAttempt(String method, double amount) {
    _analytics.track('payment_attempt', {
      'method': method,
      'amount': amount,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  void trackPaymentSuccess(String method, double amount, String orderId) {
    _analytics.track('payment_success', {
      'method': method,
      'amount': amount,
      'order_id': orderId,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  void trackPaymentFailure(String method, String errorCode, String errorMessage) {
    _analytics.track('payment_failure', {
      'method': method,
      'error_code': errorCode,
      'error_message': errorMessage,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }
}
```

---

## ⚡ 性能优化策略

### 支付SDK懒加载
```dart
// lib/core/payment/services/payment_service_factory.dart
@injectable
class PaymentServiceFactory {
  final Map<String, PaymentService> _services = {};
  final GetIt _getIt;

  PaymentServiceFactory(this._getIt);

  Future<PaymentService> getService(PaymentMethodType type) async {
    final key = type.toString();

    if (_services.containsKey(key)) {
      return _services[key]!;
    }

    // 懒加载支付服务
    final service = await _createService(type);
    _services[key] = service;
    return service;
  }

  Future<PaymentService> _createService(PaymentMethodType type) async {
    switch (type) {
      case PaymentMethodType.wechat:
        return _getIt<WeChatPaymentService>();
      case PaymentMethodType.alipay:
        return _getIt<AliPayService>();
      case PaymentMethodType.applePay:
        return _getIt<ApplePayService>();
      case PaymentMethodType.googlePay:
        return _getIt<GooglePayService>();
    }
  }
}
```

### 支付缓存策略
```dart
// lib/core/payment/cache/payment_cache_manager.dart
@injectable
class PaymentCacheManager {
  final SharedPreferences _prefs;
  static const String _cachePrefix = 'payment_cache_';
  static const Duration _cacheExpiry = Duration(minutes: 30);

  PaymentCacheManager(this._prefs);

  Future<void> cachePaymentParams(String orderId, Map<String, dynamic> params) async {
    final cacheData = {
      'params': params,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    await _prefs.setString('$_cachePrefix$orderId', jsonEncode(cacheData));
  }

  Map<String, dynamic>? getCachedPaymentParams(String orderId) {
    final cacheString = _prefs.getString('$_cachePrefix$orderId');
    if (cacheString == null) return null;

    final cacheData = jsonDecode(cacheString) as Map<String, dynamic>;
    final timestamp = cacheData['timestamp'] as int;
    final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);

    // 检查缓存是否过期
    if (DateTime.now().difference(cacheTime) > _cacheExpiry) {
      _prefs.remove('$_cachePrefix$orderId');
      return null;
    }

    return cacheData['params'] as Map<String, dynamic>;
  }

  Future<void> clearExpiredCache() async {
    final keys = _prefs.getKeys().where((key) => key.startsWith(_cachePrefix));

    for (final key in keys) {
      final cacheString = _prefs.getString(key);
      if (cacheString != null) {
        final cacheData = jsonDecode(cacheString) as Map<String, dynamic>;
        final timestamp = cacheData['timestamp'] as int;
        final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);

        if (DateTime.now().difference(cacheTime) > _cacheExpiry) {
          await _prefs.remove(key);
        }
      }
    }
  }
}
```

### 内存管理优化
```dart
// lib/core/payment/memory/payment_memory_manager.dart
@injectable
class PaymentMemoryManager {
  final Map<String, Timer> _timers = {};

  void scheduleCleanup(String key, Duration delay) {
    // 取消之前的定时器
    _timers[key]?.cancel();

    // 设置新的清理定时器
    _timers[key] = Timer(delay, () {
      _cleanup(key);
      _timers.remove(key);
    });
  }

  void _cleanup(String key) {
    // 清理支付相关的临时数据
    // 释放不必要的资源
  }

  void dispose() {
    for (final timer in _timers.values) {
      timer.cancel();
    }
    _timers.clear();
  }
}
```

---

## 🔒 安全最佳实践

### 支付参数加密
```dart
// lib/core/security/payment_encryption.dart
@injectable
class PaymentEncryption {
  final FlutterSecureStorage _secureStorage;

  PaymentEncryption(this._secureStorage);

  Future<String> encryptSensitiveData(String data) async {
    // 使用AES加密敏感数据
    final key = await _getOrCreateEncryptionKey();
    final encrypted = AESCrypt.encrypt(data, key);
    return encrypted;
  }

  Future<String> decryptSensitiveData(String encryptedData) async {
    final key = await _getOrCreateEncryptionKey();
    final decrypted = AESCrypt.decrypt(encryptedData, key);
    return decrypted;
  }

  Future<String> _getOrCreateEncryptionKey() async {
    const keyName = 'payment_encryption_key';
    String? key = await _secureStorage.read(key: keyName);

    if (key == null) {
      key = _generateRandomKey();
      await _secureStorage.write(key: keyName, value: key);
    }

    return key;
  }

  String _generateRandomKey() {
    final random = Random.secure();
    final bytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64Encode(bytes);
  }
}
```

### 支付数据验证
```dart
// lib/core/security/payment_validator.dart
@injectable
class PaymentValidator {
  bool validatePaymentAmount(double amount) {
    // 验证支付金额
    if (amount <= 0) return false;
    if (amount > 999999.99) return false; // 最大支付限额

    // 检查小数位数
    final amountString = amount.toStringAsFixed(2);
    final decimalPart = amountString.split('.')[1];
    return decimalPart.length <= 2;
  }

  bool validateOrderId(String orderId) {
    // 验证订单ID格式
    final regex = RegExp(r'^[a-zA-Z0-9_-]{8,32}$');
    return regex.hasMatch(orderId);
  }

  bool validatePaymentParams(Map<String, dynamic> params) {
    // 验证支付参数完整性
    final requiredKeys = ['amount', 'currency', 'description', 'orderId'];

    for (final key in requiredKeys) {
      if (!params.containsKey(key) || params[key] == null) {
        return false;
      }
    }

    return true;
  }

  String sanitizeInput(String input) {
    // 清理用户输入，防止注入攻击
    return input
        .replaceAll(RegExp(r'[<>"\']'), '')
        .replaceAll(RegExp(r'script', caseSensitive: false), '')
        .trim();
  }
}
```

### 网络安全配置
```dart
// lib/core/security/network_security.dart
@injectable
class NetworkSecurity {
  static const List<String> allowedHosts = [
    'api.mch.weixin.qq.com',
    'openapi.alipay.com',
    'your-backend-api.com',
  ];

  bool isHostAllowed(String url) {
    final uri = Uri.parse(url);
    return allowedHosts.contains(uri.host);
  }

  Map<String, String> getSecureHeaders() {
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': 'FlutterScaffold/1.0.0',
      'X-Requested-With': 'XMLHttpRequest',
    };
  }

  bool validateSSLCertificate(X509Certificate cert, String host, int port) {
    // 实现SSL证书验证逻辑
    // 在生产环境中应该进行严格的证书验证
    return true;
  }
}
```

---

## 🚀 实际部署指南

### 环境配置管理
```dart
// lib/core/config/environment_config.dart
enum Environment { development, staging, production }

class EnvironmentConfig {
  static Environment get current {
    const env = String.fromEnvironment('ENVIRONMENT', defaultValue: 'development');
    switch (env) {
      case 'staging':
        return Environment.staging;
      case 'production':
        return Environment.production;
      default:
        return Environment.development;
    }
  }

  static PaymentEnvironmentConfig get payment {
    switch (current) {
      case Environment.development:
        return PaymentEnvironmentConfig.development();
      case Environment.staging:
        return PaymentEnvironmentConfig.staging();
      case Environment.production:
        return PaymentEnvironmentConfig.production();
    }
  }
}

class PaymentEnvironmentConfig {
  final String wechatAppId;
  final String alipayScheme;
  final String apiBaseUrl;
  final bool enableDebugLogs;

  const PaymentEnvironmentConfig({
    required this.wechatAppId,
    required this.alipayScheme,
    required this.apiBaseUrl,
    required this.enableDebugLogs,
  });

  factory PaymentEnvironmentConfig.development() {
    return const PaymentEnvironmentConfig(
      wechatAppId: 'wx_dev_app_id',
      alipayScheme: 'dev_app_scheme',
      apiBaseUrl: 'https://dev-api.example.com',
      enableDebugLogs: true,
    );
  }

  factory PaymentEnvironmentConfig.staging() {
    return const PaymentEnvironmentConfig(
      wechatAppId: 'wx_staging_app_id',
      alipayScheme: 'staging_app_scheme',
      apiBaseUrl: 'https://staging-api.example.com',
      enableDebugLogs: true,
    );
  }

  factory PaymentEnvironmentConfig.production() {
    return const PaymentEnvironmentConfig(
      wechatAppId: 'wx_prod_app_id',
      alipayScheme: 'prod_app_scheme',
      apiBaseUrl: 'https://api.example.com',
      enableDebugLogs: false,
    );
  }
}
```

### CI/CD 配置示例
```yaml
# .github/workflows/payment_integration.yml
name: Payment Integration CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3

    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.32.5'

    - name: Install dependencies
      run: flutter pub get

    - name: Run payment tests
      run: flutter test test/features/payment/

    - name: Run integration tests
      run: flutter drive --driver=test_driver/integration_test.dart --target=integration_test/payment_flow_test.dart

  build_android:
    needs: test
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3

    - name: Setup Flutter
      uses: subosito/flutter-action@v2

    - name: Build Android APK
      run: |
        flutter build apk --release \
          --dart-define=ENVIRONMENT=production \
          --dart-define=WECHAT_APP_ID=${{ secrets.WECHAT_APP_ID }} \
          --dart-define=ALIPAY_SCHEME=${{ secrets.ALIPAY_SCHEME }}

  build_ios:
    needs: test
    runs-on: macos-latest
    steps:
    - uses: actions/checkout@v3

    - name: Setup Flutter
      uses: subosito/flutter-action@v2

    - name: Build iOS
      run: |
        flutter build ios --release --no-codesign \
          --dart-define=ENVIRONMENT=production \
          --dart-define=WECHAT_APP_ID=${{ secrets.WECHAT_APP_ID }} \
          --dart-define=ALIPAY_SCHEME=${{ secrets.ALIPAY_SCHEME }}
```

---

**注意**: 本文档提供了完整的技术实施路径、代码示例、错误处理机制、性能优化、安全最佳实践和实际部署指南。每个阶段都可以独立开发和测试，确保项目风险可控。所有代码示例都基于项目现有的Clean Architecture架构和BLoC状态管理模式，并遵循Flutter最佳实践。

## 📞 技术支持和后续维护

### 开发团队协作要点
1. **前端团队**: 负责UI实现、状态管理、错误处理
2. **后端团队**: 负责支付接口、订单管理、安全验证
3. **测试团队**: 负责功能测试、安全测试、性能测试
4. **运维团队**: 负责环境配置、监控告警、日志分析

### 维护和更新策略
- **定期更新**: 每季度检查SDK版本更新
- **安全审计**: 每半年进行一次安全审计
- **性能监控**: 实时监控支付成功率和响应时间
- **用户反馈**: 建立用户反馈收集和处理机制

此文档将随着项目发展持续更新和完善。

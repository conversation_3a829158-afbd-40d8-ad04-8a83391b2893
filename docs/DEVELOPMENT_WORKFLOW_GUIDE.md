# Flutter Scaffold 开发工作流完整指南

## 📋 概述

本文档是 Flutter Scaffold 项目的完整开发工作流指南，整合了环境配置、项目结构、代码生成、Mason模板使用、Web平台开发等所有开发相关内容，为开发者提供一站式的开发指导。

## 🚀 快速开始

### 前置要求
- Flutter 3.32.5+
- Dart 3.6.0+
- Git
- Make (可选)

### 环境设置
```bash
# 1. 克隆项目
git clone https://github.com/your-org/flutter-scaffold.git
cd flutter-scaffold

# 2. 环境设置 (推荐使用make)
make setup                  # 一键设置开发环境
mise run setup              # 或使用: mise r s

# 3. 依赖安装 (已包含在setup中)
make bootstrap              # 手动安装依赖
mise run bootstrap          # 或使用: mise r bs

# 4. 代码生成
make gen                    # 生成所有代码
mise run gen                # 或使用: mise r g

# 5. 运行应用
make run-dev                # 运行开发环境
mise run run-dev            # 或使用: mise r dev
```

## 🏗️ 项目结构详解

### Monorepo 架构
```
flutter_scaffold/
├── apps/
│   └── mobile/                 # 主应用
│       ├── lib/
│       │   ├── core/          # 核心功能
│       │   ├── features/      # 功能模块
│       │   └── main*.dart     # 多环境入口
│       ├── test/              # 测试文件
│       └── pubspec.yaml
├── packages/
│   ├── ui_library/            # UI组件库
│   └── data_models/           # 数据模型
├── bricks/                    # Mason模板
├── docs/                      # 项目文档
├── scripts/                   # 构建脚本
├── melos.yaml                 # Monorepo配置
└── Makefile                   # 构建命令
```

### 核心目录说明

#### apps/mobile/lib/ 结构
```
lib/
├── core/                      # 核心基础设施
│   ├── di/                   # 依赖注入
│   ├── router/               # 路由管理
│   ├── theme/                # 主题系统
│   ├── network/              # 网络层
│   ├── storage/              # 存储服务
│   ├── error/                # 错误处理
│   ├── constants/            # 常量定义
│   ├── utils/                # 工具类
│   ├── extensions/           # 扩展方法
│   ├── validators/           # 验证器
│   ├── performance/          # 性能优化
│   ├── security/             # 安全服务
│   └── dev_tools/            # 开发工具
├── features/                  # 功能模块 (Clean Architecture)
│   ├── auth/                 # 认证模块
│   │   ├── data/            # 数据层
│   │   ├── domain/          # 领域层
│   │   └── presentation/    # 表现层
│   ├── home/                 # 首页模块
│   ├── profile/              # 用户资料模块
│   └── settings/             # 设置模块
├── shared/                    # 共享组件
│   ├── widgets/              # 通用Widget
│   ├── models/               # 共享模型
│   └── services/             # 共享服务
└── main*.dart                 # 多环境入口文件
```

## ⚙️ 代码生成系统

### 双重代码生成架构

Flutter Scaffold 使用 **Mason + build_runner** 双重代码生成系统：

#### 🧱 Mason - 结构生成器
- **作用**: 生成完整的文件结构和基础代码
- **优势**: 快速搭建功能模块骨架，确保架构一致性
- **使用场景**: 创建新功能、页面、组件等

#### ⚙️ build_runner - 注解处理器
- **作用**: 处理注解，生成辅助代码
- **优势**: 自动化重复代码，确保类型安全
- **使用场景**: 序列化、依赖注入、路由等

### 代码生成工作流

#### 1. 基础命令
```bash
# 完整代码生成 (推荐使用make)
make gen                    # 生成所有代码
mise run gen                # 或使用: mise r g

# 监听模式 (推荐开发时使用)
dart run build_runner watch --delete-conflicting-outputs

# 清理重新生成
make clean                  # 清理所有包
mise run clean              # 或使用: mise r c
make gen                    # 重新生成
mise run gen                # 或使用: mise r g

# 分步生成 (解决冲突时使用)
dart run build_runner build --build-filter="**/*.freezed.dart" --delete-conflicting-outputs
dart run build_runner build --build-filter="**/*.g.dart" --delete-conflicting-outputs
dart run build_runner build --build-filter="**/*.config.dart" --delete-conflicting-outputs
```

#### 2. Mason 模板使用

##### 可用模板列表
```bash
# 查看所有可用模板 (推荐使用make)
make mason-list             # 列出所有模板
mise run mason-list         # 或使用: mise r ml
mason list                  # 或直接使用mason

# 输出示例:
# ├── feature      # 完整功能模块 (推荐)
# ├── page         # 单页面
# ├── widget       # 自定义Widget
# ├── bloc         # BLoC状态管理
# ├── repository   # 数据仓库
# ├── model        # 数据模型
# ├── service      # 服务类
# ├── validator    # 验证器
# ├── extension    # 扩展方法
# ├── test         # 测试文件
# ├── api          # API接口
# ├── entity       # 领域实体
# ├── usecase      # 用例
# └── adr          # 架构决策记录
```

##### 创建完整功能模块 (推荐)
```bash
# 创建完整的功能模块 (推荐使用make)
make feature                # 交互式创建功能模块
mise run feature            # 或使用: mise r feat
# 或直接使用mason (需要手动输入参数):
mason make feature --name user_management --entity user

# 生成的文件结构:
# lib/features/user_management/
# ├── data/
# │   ├── datasources/
# │   │   ├── user_local_datasource.dart
# │   │   └── user_remote_datasource.dart
# │   ├── models/
# │   │   └── user_model.dart
# │   └── repositories/
# │       └── user_repository_impl.dart
# ├── domain/
# │   ├── entities/
# │   │   └── user.dart
# │   ├── repositories/
# │   │   └── user_repository.dart
# │   └── usecases/
# │       ├── get_user.dart
# │       ├── create_user.dart
# │       └── update_user.dart
# └── presentation/
#     ├── bloc/
#     │   ├── user_bloc.dart
#     │   ├── user_event.dart
#     │   └── user_state.dart
#     ├── pages/
#     │   └── user_management_page.dart
#     └── widgets/
#         └── user_list_widget.dart
```

##### 创建单独组件
```bash
# 创建页面 (推荐使用make)
make page                   # 交互式创建页面
mise run page               # 或使用: mise r pg
# 或直接使用mason:
mason make page --name settings --feature settings

# 创建Widget
make widget                 # 交互式创建组件
mise run widget             # 或使用: mise r wg
# 或直接使用mason:
mason make widget --name custom_button --feature shared

# 创建验证器
make validator              # 交互式创建验证器
mise run validator          # 或使用: mise r val
# 或直接使用mason:
mason make validator --name email_validator

# 创建架构决策记录
make adr                    # 交互式创建ADR
mise run adr                # 或使用: mise r adr
# 或直接使用mason:
mason make adr --name "implement-user-authentication"

# 其他模板 (直接使用mason)
mason make bloc --name theme --feature settings
mason make model --name user_profile
mason make service --name notification_service
```

#### 3. build_runner 注解处理

##### 支持的注解类型
```dart
// 1. 数据序列化 (@JsonSerializable)
@JsonSerializable()
class UserModel {
  final String id;
  final String name;

  UserModel({required this.id, required this.name});

  // 生成: fromJson, toJson 方法
}

// 2. 不可变数据类 (@freezed)
@freezed
class UserState with _$UserState {
  const factory UserState.initial() = _Initial;
  const factory UserState.loading() = _Loading;
  const factory UserState.loaded(List<User> users) = _Loaded;
  const factory UserState.error(String message) = _Error;

  // 生成: copyWith, ==, hashCode, toString 等
}

// 3. 依赖注入 (@injectable)
@injectable
class UserRepository {
  final UserRemoteDataSource _remoteDataSource;
  final UserLocalDataSource _localDataSource;

  UserRepository(this._remoteDataSource, this._localDataSource);

  // 自动注册到依赖注入容器
}

// 4. 路由生成 (@AutoRoute)
@AutoRoute()
class AppRouter extends _$AppRouter {
  @override
  List<AutoRoute> get routes => [
    // 自动生成路由配置
  ];
}
```

##### 生成命令详解
```bash
# 生成所有注解代码
dart run build_runner build --delete-conflicting-outputs

# 监听模式 (开发推荐)
dart run build_runner watch --delete-conflicting-outputs

# 只生成特定类型
dart run build_runner build --build-filter="**/*.g.dart"
dart run build_runner build --build-filter="**/*.freezed.dart"
dart run build_runner build --build-filter="**/*.config.dart"

# 清理后重新生成
flutter clean
dart run build_runner clean
dart run build_runner build --delete-conflicting-outputs
```

### 🚨 代码生成故障排除

#### 常见问题及解决方案

##### 1. 循环依赖错误
```bash
# 错误信息
E injectable_generator:injectable_builder on lib/core/di/di.dart:
  Bad state: Cannot recurse at later or equal phase 8

# 解决方案
# 1. 注释循环导入
# 编辑 lib/core/di/di.dart，临时注释: import 'di.config.dart';

# 2. 分步生成
dart run build_runner build --build-filter="**/*.freezed.dart" --delete-conflicting-outputs
dart run build_runner build --build-filter="**/*.g.dart" --delete-conflicting-outputs

# 3. 恢复导入并生成 Injectable
# 取消注释: import 'di.config.dart';
dart run build_runner build --build-filter="**/*.config.dart" --delete-conflicting-outputs
```

##### 2. 构建阶段冲突
```bash
# 错误信息: "Cannot recurse at later or equal phase X"
# 解决方案: 清理并分步生成

flutter clean
rm -rf .dart_tool
flutter pub get
# 然后按上述步骤分步生成
```

##### 3. 一键修复脚本
```bash
# 使用项目提供的修复脚本
./scripts/fix_codegen.sh

# 或手动执行修复步骤
make clean && make bootstrap && make gen
```

#### 最佳实践

##### 1. 开发工作流
```bash
# 推荐的日常开发流程
# 1. 启动监听模式
dart run build_runner watch --delete-conflicting-outputs

# 2. 编写代码（模型、API、服务等）

# 3. 保存文件，自动触发生成

# 4. 如遇问题，停止监听并手动修复
Ctrl+C
./scripts/fix_codegen.sh
```

##### 2. 团队协作规范
- **提交前检查**: 确保所有生成文件都已更新
- **忽略规则**: `.gitignore` 中正确配置生成文件
- **版本控制**: 生成文件应该被提交到版本控制

##### 3. 性能优化
```bash
# 只生成必要的文件
dart run build_runner build --build-filter="lib/features/auth/**/*.g.dart"

# 使用并行构建
dart run build_runner build --delete-conflicting-outputs --verbose
```

## 🌐 Web 平台开发

### Web 平台配置

#### 1. 基础配置
```yaml
# pubspec.yaml - Web 特定依赖
dependencies:
  flutter:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter
  url_strategy: ^0.2.0  # URL 策略
  js: ^0.6.4           # JavaScript 互操作

dev_dependencies:
  build_web_compilers: ^4.0.0
```

#### 2. Web 入口配置
```html
<!-- web/index.html -->
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Flutter Scaffold</title>

  <!-- PWA 配置 -->
  <link rel="manifest" href="manifest.json">
  <meta name="theme-color" content="#2196F3">

  <!-- 预加载关键资源 -->
  <link rel="preload" href="fonts/MaterialIcons-Regular.otf" as="font" crossorigin>
</head>
<body>
  <div id="loading">
    <!-- 自定义加载动画 -->
  </div>
  <script src="main.dart.js"></script>
</body>
</html>
```

#### 3. PWA 配置
```json
// web/manifest.json
{
  "name": "Flutter Scaffold",
  "short_name": "Scaffold",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#FFFFFF",
  "theme_color": "#2196F3",
  "icons": [
    {
      "src": "icons/icon-192.png",
      "sizes": "192x192",
      "type": "image/png"
    },
    {
      "src": "icons/icon-512.png",
      "sizes": "512x512",
      "type": "image/png"
    }
  ]
}
```

### Web 平台特定实现

#### 1. 平台检测
```dart
import 'package:flutter/foundation.dart';

class PlatformUtils {
  static bool get isWeb => kIsWeb;
  static bool get isMobile => !kIsWeb;

  static void webOnlyFunction() {
    if (kIsWeb) {
      // Web 平台特定实现
    }
  }
}
```

#### 2. 响应式布局
```dart
class ResponsiveLayout extends StatelessWidget {
  final Widget mobile;
  final Widget tablet;
  final Widget desktop;

  const ResponsiveLayout({
    Key? key,
    required this.mobile,
    required this.tablet,
    required this.desktop,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth < 768) {
          return mobile;
        } else if (constraints.maxWidth < 1200) {
          return tablet;
        } else {
          return desktop;
        }
      },
    );
  }
}
```

#### 3. Web 路由配置
```dart
import 'package:url_strategy/url_strategy.dart';

void main() {
  // 移除 URL 中的 # 号
  setPathUrlStrategy();

  runApp(MyApp());
}
```

### Web 构建和部署

#### 1. 开发构建
```bash
# Web 开发服务器
flutter run -d chrome --web-port 8080

# 热重载
flutter run -d chrome --hot
```

#### 2. 生产构建
```bash
# 构建 Web 应用
flutter build web --release

# 优化构建
flutter build web --release --web-renderer canvaskit --dart-define=FLUTTER_WEB_USE_SKIA=true

# 分析构建大小
flutter build web --analyze-size
```

#### 3. 部署配置

##### Firebase Hosting
```json
// firebase.json
{
  "hosting": {
    "public": "build/web",
    "ignore": [
      "firebase.json",
      "**/.*",
      "**/node_modules/**"
    ],
    "rewrites": [
      {
        "source": "**",
        "destination": "/index.html"
      }
    ],
    "headers": [
      {
        "source": "**/*.@(js|css|woff2)",
        "headers": [
          {
            "key": "Cache-Control",
            "value": "max-age=31536000"
          }
        ]
      }
    ]
  }
}
```

##### Nginx 配置
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/flutter-app;
    index index.html;

    # 启用 gzip 压缩
    gzip on;
    gzip_types text/css application/javascript application/json;

    # 缓存静态资源
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # SPA 路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

### Web 性能优化

#### 1. 代码分割
```dart
// 使用延迟加载
import 'package:flutter/material.dart';

class LazyLoadedPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: _loadHeavyWidget(),
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          return snapshot.data!;
        }
        return CircularProgressIndicator();
      },
    );
  }

  Future<Widget> _loadHeavyWidget() async {
    // 延迟加载重型组件
    await Future.delayed(Duration(milliseconds: 100));
    return HeavyWidget();
  }
}
```

#### 2. 资源优化
```yaml
# pubspec.yaml - 字体优化
flutter:
  fonts:
    - family: Roboto
      fonts:
        - asset: fonts/Roboto-Regular.ttf
        - asset: fonts/Roboto-Bold.ttf
          weight: 700

  # 图片优化
  assets:
    - assets/images/
    - assets/icons/
```

#### 3. 网络优化
```dart
class WebNetworkOptimizer {
  static void configureHttpClient() {
    if (kIsWeb) {
      // Web 平台网络配置
      HttpOverrides.global = WebHttpOverrides();
    }
  }
}

class WebHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    final client = super.createHttpClient(context);
    client.connectionTimeout = Duration(seconds: 10);
    return client;
  }
}
```

## 📦 构建和部署

### 多环境构建

#### 1. 环境配置
```bash
# 开发环境
flutter run --flavor development -t lib/main_development.dart

# 测试环境
flutter run --flavor staging -t lib/main_staging.dart

# 生产环境
flutter run --flavor production -t lib/main_production.dart
```

#### 2. 平台构建

##### Android
```bash
# Debug APK
flutter build apk --flavor development --debug

# Release APK
flutter build apk --flavor production --release --shrink --obfuscate

# Android App Bundle (推荐)
flutter build appbundle --flavor production --release
```

##### iOS
```bash
# Debug
flutter build ios --flavor development --debug --no-codesign

# Release
flutter build ios --flavor production --release --no-codesign
```

##### Web
```bash
# Debug
flutter build web --debug

# Release
flutter build web --release --web-renderer canvaskit
```

##### Desktop
```bash
# Windows
flutter build windows --release

# macOS
flutter build macos --release

# Linux
flutter build linux --release
```

### CI/CD 配置

#### GitHub Actions
```yaml
# .github/workflows/build.yml
name: Build and Test

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.32.5'

    - name: Install dependencies
      run: |
        flutter pub get
        melos bootstrap

    - name: Run tests
      run: |
        make test
        make analyze

    - name: Build APK
      run: flutter build apk --release
```

## 🎯 开发最佳实践

### 代码规范
- 使用 `dart format` 格式化代码
- 遵循 `flutter analyze` 的静态分析规则
- 使用 `freezed` 进行数据类生成
- 保持函数单一职责

### 性能优化
- 使用 `const` 构造函数
- 合理使用 `ListView.builder`
- 图片使用 `cached_network_image`
- 避免不必要的重建

### 安全考虑
- 敏感信息使用 `flutter_secure_storage`
- 网络请求使用 HTTPS
- 输入验证和清理
- 定期更新依赖包

### 测试策略
- 单元测试覆盖核心业务逻辑
- Widget测试验证UI组件
- 集成测试确保端到端功能
- 使用 `mockito` 进行依赖模拟

### 版本控制
- 使用语义化版本号
- 提交信息遵循约定式提交
- 定期合并主分支
- 保护主分支，要求代码审查

## 🔧 开发工具集成

### IDE 配置

#### VS Code 推荐插件
```json
// .vscode/extensions.json
{
  "recommendations": [
    "dart-code.dart-code",
    "dart-code.flutter",
    "ms-vscode.vscode-json",
    "bradlc.vscode-tailwindcss",
    "usernamehw.errorlens",
    "gruntfuggly.todo-tree"
  ]
}
```

#### VS Code 设置
```json
// .vscode/settings.json
{
  "dart.flutterSdkPath": "/path/to/flutter",
  "dart.lineLength": 120,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll": true
  },
  "files.associations": {
    "*.dart": "dart"
  }
}
```

### 调试配置

#### launch.json 配置
```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Development",
      "request": "launch",
      "type": "dart",
      "program": "apps/mobile/lib/main_development.dart",
      "args": ["--flavor", "development"]
    },
    {
      "name": "Production",
      "request": "launch",
      "type": "dart",
      "program": "apps/mobile/lib/main_production.dart",
      "args": ["--flavor", "production"]
    }
  ]
}
```

### 代码质量工具

#### 静态分析配置
```yaml
# analysis_options.yaml
include: package:flutter_lints/flutter.yaml

analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
    - "**/*.config.dart"

  strong-mode:
    implicit-casts: false
    implicit-dynamic: false

linter:
  rules:
    # 性能相关
    - avoid_unnecessary_containers
    - use_decorated_box
    - sized_box_for_whitespace

    # 代码风格
    - prefer_const_constructors
    - prefer_const_literals_to_create_immutables
    - prefer_final_fields

    # 错误预防
    - avoid_print
    - avoid_web_libraries_in_flutter
    - cancel_subscriptions
```

#### Pre-commit Hooks
```yaml
# .pre-commit-config.yaml
repos:
  - repo: local
    hooks:
      - id: flutter-analyze
        name: Flutter Analyze
        entry: flutter analyze
        language: system
        pass_filenames: false

      - id: flutter-test
        name: Flutter Test
        entry: flutter test
        language: system
        pass_filenames: false

      - id: dart-format
        name: Dart Format
        entry: dart format --set-exit-if-changed .
        language: system
        pass_filenames: false
```

## 🚀 高级开发技巧

### 性能分析

#### 1. 性能监控
```dart
// lib/core/performance/performance_monitor.dart
class PerformanceMonitor {
  static void trackPageLoad(String pageName) {
    final stopwatch = Stopwatch()..start();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      stopwatch.stop();
      print('Page $pageName loaded in ${stopwatch.elapsedMilliseconds}ms');
    });
  }

  static void trackWidgetBuild(String widgetName, VoidCallback build) {
    final stopwatch = Stopwatch()..start();
    build();
    stopwatch.stop();

    if (stopwatch.elapsedMilliseconds > 16) {
      print('Warning: $widgetName took ${stopwatch.elapsedMilliseconds}ms to build');
    }
  }
}
```

#### 2. 内存优化
```dart
// 使用 WeakReference 避免内存泄漏
class CacheManager {
  final Map<String, WeakReference<Widget>> _cache = {};

  Widget? getWidget(String key) {
    final ref = _cache[key];
    return ref?.target;
  }

  void cacheWidget(String key, Widget widget) {
    _cache[key] = WeakReference(widget);
  }
}
```

### 国际化配置

#### 1. 基础配置
```yaml
# pubspec.yaml
dependencies:
  flutter_localizations:
    sdk: flutter
  intl: ^0.18.0

flutter:
  generate: true
```

#### 2. 配置文件
```yaml
# l10n.yaml
arb-dir: lib/l10n
template-arb-file: app_en.arb
output-localization-file: app_localizations.dart
```

#### 3. 本地化文件
```json
// lib/l10n/app_en.arb
{
  "appTitle": "Flutter Scaffold",
  "welcome": "Welcome to Flutter Scaffold",
  "login": "Login",
  "logout": "Logout"
}
```

```json
// lib/l10n/app_zh.arb
{
  "appTitle": "Flutter 脚手架",
  "welcome": "欢迎使用 Flutter 脚手架",
  "login": "登录",
  "logout": "退出登录"
}
```

### 错误处理和日志

#### 1. 全局错误处理
```dart
// lib/core/error/global_error_handler.dart
class GlobalErrorHandler {
  static void initialize() {
    FlutterError.onError = (FlutterErrorDetails details) {
      FlutterError.presentError(details);
      _logError(details.exception, details.stack);
    };

    PlatformDispatcher.instance.onError = (error, stack) {
      _logError(error, stack);
      return true;
    };
  }

  static void _logError(Object error, StackTrace? stack) {
    // 发送到错误监控服务
    print('Error: $error');
    if (stack != null) {
      print('Stack trace: $stack');
    }
  }
}
```

#### 2. 结构化日志
```dart
// lib/core/logging/app_logger.dart
enum LogLevel { debug, info, warning, error }

class AppLogger {
  static void log(LogLevel level, String message, [Object? error, StackTrace? stackTrace]) {
    final timestamp = DateTime.now().toIso8601String();
    final levelStr = level.toString().split('.').last.toUpperCase();

    print('[$timestamp] [$levelStr] $message');

    if (error != null) {
      print('Error: $error');
    }

    if (stackTrace != null) {
      print('Stack trace: $stackTrace');
    }
  }

  static void debug(String message) => log(LogLevel.debug, message);
  static void info(String message) => log(LogLevel.info, message);
  static void warning(String message) => log(LogLevel.warning, message);
  static void error(String message, [Object? error, StackTrace? stackTrace]) =>
    log(LogLevel.error, message, error, stackTrace);
}
```

## 🔒 安全开发实践

### 1. 数据加密
```dart
// lib/core/security/encryption_service.dart
import 'dart:convert';
import 'package:crypto/crypto.dart';

class EncryptionService {
  static String hashPassword(String password, String salt) {
    final bytes = utf8.encode(password + salt);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  static String generateSalt() {
    final random = Random.secure();
    final saltBytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64.encode(saltBytes);
  }
}
```

### 2. 网络安全
```dart
// lib/core/network/secure_http_client.dart
class SecureHttpClient {
  static Dio createSecureClient() {
    final dio = Dio();

    // 添加证书固定
    (dio.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate = (client) {
      client.badCertificateCallback = (cert, host, port) {
        // 验证证书
        return _verifyCertificate(cert, host);
      };
      return client;
    };

    // 添加安全头
    dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        options.headers['X-Requested-With'] = 'XMLHttpRequest';
        options.headers['X-Content-Type-Options'] = 'nosniff';
        handler.next(options);
      },
    ));

    return dio;
  }

  static bool _verifyCertificate(X509Certificate cert, String host) {
    // 实现证书验证逻辑
    return true;
  }
}
```

### 3. 输入验证
```dart
// lib/core/validators/security_validators.dart
class SecurityValidators {
  static bool isValidEmail(String email) {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(email);
  }

  static bool isStrongPassword(String password) {
    // 至少8位，包含大小写字母、数字和特殊字符
    return RegExp(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$').hasMatch(password);
  }

  static String sanitizeInput(String input) {
    // 清理潜在的恶意输入
    return input
        .replaceAll(RegExp(r'<script[^>]*>.*?</script>', caseSensitive: false), '')
        .replaceAll(RegExp(r'javascript:', caseSensitive: false), '')
        .trim();
  }
}
```

## 📊 监控和分析

### 1. 应用性能监控
```dart
// lib/core/analytics/performance_tracker.dart
class PerformanceTracker {
  static void trackScreenView(String screenName) {
    // 发送到分析服务
    print('Screen view: $screenName');
  }

  static void trackEvent(String eventName, Map<String, dynamic> parameters) {
    // 发送事件到分析服务
    print('Event: $eventName, Parameters: $parameters');
  }

  static void trackError(String error, String? stackTrace) {
    // 发送错误到监控服务
    print('Error tracked: $error');
  }
}
```

### 2. 用户行为分析
```dart
// lib/core/analytics/user_analytics.dart
class UserAnalytics {
  static void setUserId(String userId) {
    // 设置用户ID
  }

  static void setUserProperty(String name, String value) {
    // 设置用户属性
  }

  static void trackUserAction(String action, Map<String, dynamic> context) {
    // 追踪用户行为
    PerformanceTracker.trackEvent('user_action', {
      'action': action,
      ...context,
    });
  }
}
```

## 📚 相关文档

- [Clean Architecture 指南](CLEAN_ARCHITECTURE_GUIDE_PRO.md)
- [状态管理指南](STATE_MANAGEMENT_GUIDE.md)
- [UI 设计指南](UI_GUIDE_PRO.md)
- [后端 API 指南](BACKEND_API_GUIDE.md)
- [Dart 开发指南](DART_GUIDE_PRO.md)

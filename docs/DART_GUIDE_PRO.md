# Flutter Scaffold Dart 开发指南 - 企业级Dart最佳实践

## 🎯 基于项目实际应用的Dart深度指南

> **Flutter Scaffold Dart开发理念**
> 基于现代Dart特性和企业级开发实践，展示在Flutter Scaffold项目中如何运用Dart的强大功能。从空安全到代码生成，从函数式编程到异步处理，每一个特性都有实际的应用场景。

---

## 📚 核心内容概览

### 🏗️ 第一部分：项目架构中的Dart特性

- [空安全与类型系统](#第一章空安全与类型系统---flutter-scaffold的类型安全实践)
- [代码生成与注解](#第二章代码生成与注解---自动化开发的核心)
- [函数式编程模式](#第三章函数式编程模式---either与函数组合)
- [异步编程最佳实践](#第四章异步编程最佳实践---future与stream的企业级应用)

### 🎨 第二部分：数据建模与序列化

- [Freezed数据类](#第五章freezed数据类---不可变数据的艺术)
- [JSON序列化策略](#第六章json序列化策略---json_annotation的高级用法)
- [数据验证与转换](#第七章数据验证与转换---类型安全的数据处理)

### 🔧 第三部分：依赖注入与架构模式

- [Injectable依赖注入](#第八章injectable依赖注入---企业级依赖管理)
- [Repository模式实现](#第九章repository模式实现---数据层的抽象艺术)
- [UseCase模式应用](#第十章usecase模式应用---业务逻辑的封装)

### ⚡ 第四部分：性能优化与最佳实践

- [内存管理与性能优化](#第十一章内存管理与性能优化)
- [代码规范与团队协作](#第十二章代码规范与团队协作)
- [测试策略与质量保证](#第十三章测试策略与质量保证)

### 第六部分：实战篇

- [第23章：完整项目实战](#第二十三章完整项目实战)
- [第24章：常见问题与解决方案](#第二十四章常见问题与解决方案)
- [第25章：工具链与开发环境](#第二十五章工具链与开发环境)


---

## 第一章：空安全与类型系统 - Flutter Scaffold的类型安全实践

### 🛡️ 空安全：消灭运行时空指针异常

在Flutter Scaffold项目中，我们充分利用Dart的空安全特性，确保代码的健壮性和可维护性。

#### 项目中的空安全实践

```dart
// ✅ 项目实际的空安全应用 - User实体
@freezed
class User with _$User {
  const factory User({
    required String id,           // 非空字段，必须提供
    required String email,        // 非空字段，必须提供
    required String name,         // 非空字段，必须提供
    String? avatarUrl,           // 可空字段，可能为null
    String? phoneNumber,         // 可空字段，可能为null
    DateTime? lastLoginAt,       // 可空字段，可能为null
    @Default(MembershipLevel.regular) MembershipLevel membershipLevel,
    @Default(true) bool isActive,
  }) = _User;

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
}

// ✅ 空安全的数据处理
class UserService {
  // 返回类型明确表示可能为空
  Future<User?> getCurrentUser() async {
    final token = await _tokenStorage.getToken();

    // 空安全检查
    if (token == null) {
      return null; // 明确返回null，而不是抛出异常
    }

    try {
      final response = await _apiClient.getUser(token.userId);
      return response.data; // response.data可能为null
    } catch (e) {
      return null; // 错误时返回null
    }
  }

  // 使用空安全操作符
  String getUserDisplayName(User? user) {
    // 空安全链式调用
    return user?.name ?? 'Unknown User';
  }

  // 空安全的集合操作
  List<String> getUserPermissions(User? user) {
    // 如果user为null，返回空列表
    return user?.permissions ?? [];
  }

  // 空断言操作符的谨慎使用
  void updateUserProfile(User user) {
    // 只在确定不为null时使用!
    final userId = user.id; // 不需要!，因为id是非空字段

    // 谨慎使用空断言
    final avatar = user.avatarUrl!; // ❌ 危险！可能抛出异常

    // 更安全的方式
    final safeAvatar = user.avatarUrl ?? 'default_avatar.png'; // ✅ 安全
  }
}
```

#### 空安全的最佳实践

```dart
// ✅ 项目中的空安全模式
class OrderRepository {
  // 1. 明确的返回类型
  Future<Either<Failure, Order?>> findOrderById(String id) async {
    try {
      final response = await _apiClient.getOrder(id);

      // 使用空安全检查
      if (response.data == null) {
        return Left(NotFoundFailure('Order not found'));
      }

      return Right(response.data);
    } catch (e) {
      return Left(NetworkFailure(e.toString()));
    }
  }

  // 2. 空安全的集合操作
  List<Order> filterActiveOrders(List<Order?> orders) {
    return orders
        .where((order) => order != null)           // 过滤null值
        .cast<Order>()                             // 安全转换类型
        .where((order) => order.isActive)          // 进一步过滤
        .toList();
  }

  // 3. 空安全的条件检查
  bool canCancelOrder(Order? order) {
    // 空安全链式调用
    return order?.status == OrderStatus.pending &&
           order?.createdAt.isAfter(
             DateTime.now().subtract(Duration(hours: 24))
           ) == true;
  }

  // 4. 空安全的数据转换
  Map<String, dynamic> orderToJson(Order? order) {
    if (order == null) {
      return {}; // 返回空Map而不是null
    }

    return {
      'id': order.id,
      'status': order.status.name,
      'total': order.total,
      'items': order.items?.map((item) => item.toJson()).toList() ?? [],
    };
  }
}
```

---

## 第二章：代码生成与注解 - 自动化开发的核心

### 🤖 代码生成：让机器写代码

Flutter Scaffold大量使用代码生成来提高开发效率和代码质量，减少样板代码。

#### 项目中的代码生成实践

```dart
// ✅ 项目实际的代码生成配置 - build.yaml
targets:
  $default:
    builders:
      # Freezed代码生成
      freezed:
        enabled: true
        options:
          # 生成copyWith方法
          copy_with: true
          # 生成相等性比较
          equal: true
          # 生成toString方法
          to_string: true

      # JSON序列化代码生成
      json_serializable:
        enabled: true
        options:
          # 生成toJson方法
          generate_to_json: true
          # 生成fromJson方法
          generate_from_json: true
          # 字段命名策略
          field_rename: snake_case
          # 包含null字段
          include_if_null: false

      # Injectable依赖注入代码生成
      injectable_generator:
        enabled: true
        options:
          # 自动注册
          auto_register: true
          # 生成配置文件
          generate_for_dir: ['lib']

      # Retrofit API客户端代码生成
      retrofit_generator:
        enabled: true
        options:
          # 生成详细日志
          verbose: true

# ✅ 项目实际的Freezed数据类
@freezed
class CreateOrderRequest with _$CreateOrderRequest {
  const factory CreateOrderRequest({
    required String userId,
    required List<OrderItem> items,
    required ShippingAddress shippingAddress,
    required PaymentMethod paymentMethod,
    String? couponCode,
    String? notes,
  }) = _CreateOrderRequest;

  factory CreateOrderRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateOrderRequestFromJson(json);
}

// 生成的代码包括：
// - _$CreateOrderRequest 实现类
// - copyWith() 方法
// - == 操作符和 hashCode
// - toString() 方法
// - fromJson() 和 toJson() 方法

// ✅ 项目实际的Injectable依赖注入
@module
abstract class NetworkModule {
  @lazySingleton
  Dio provideDio() {
    return Dio(BaseOptions(
      baseUrl: AppConfig.apiBaseUrl,
      connectTimeout: Duration(seconds: 30),
      receiveTimeout: Duration(seconds: 30),
    ));
  }

  @lazySingleton
  ApiClient provideApiClient(Dio dio) => ApiClient(dio);
}

@injectable
class UserRepository {
  final ApiClient _apiClient;
  final UserLocalDataSource _localDataSource;

  UserRepository(this._apiClient, this._localDataSource);

  Future<Either<Failure, User>> getUser(String id) async {
    // 实现逻辑
  }
}

// 生成的代码包括：
// - GetIt注册代码
// - 依赖关系图
// - 自动工厂方法

// ✅ 项目实际的Retrofit API客户端
@RestApi(baseUrl: '/api/v1')
abstract class ApiClient {
  factory ApiClient(Dio dio, {String baseUrl}) = _ApiClient;

  @GET('/users/{id}')
  Future<ApiResponse<User>> getUser(@Path('id') String userId);

  @POST('/orders')
  Future<ApiResponse<Order>> createOrder(@Body() CreateOrderRequest request);

  @GET('/orders')
  Future<ApiResponse<PaginatedResponse<Order>>> getOrders(
    @Query('page') int page,
    @Query('limit') int limit,
    @Query('status') String? status,
  );
}

// 生成的代码包括：
// - _ApiClient 实现类
// - HTTP请求方法
// - 参数序列化
// - 响应反序列化
```

#### 自定义代码生成器

```dart
// ✅ 项目中的自定义代码生成器示例
@Target({TargetKind.classType})
class AutoRepository {
  const AutoRepository();
}

// 使用自定义注解
@AutoRepository()
class User {
  final String id;
  final String name;
  final String email;

  const User({
    required this.id,
    required this.name,
    required this.email,
  });
}

// 生成的Repository代码：
// abstract class UserRepository {
//   Future<Either<Failure, User>> findById(String id);
//   Future<Either<Failure, List<User>>> findAll();
//   Future<Either<Failure, User>> save(User user);
//   Future<Either<Failure, void>> delete(String id);
// }
```

---

## 第三章：函数式编程模式 - Either与函数组合

### 🔗 Either类型：优雅的错误处理

Flutter Scaffold使用Either类型实现类型安全的错误处理，避免异常抛出。

#### 项目中的Either应用

```dart
// ✅ 项目实际的Either错误处理
abstract class Failure {
  const Failure({required this.message, this.code});

  final String message;
  final String? code;
}

class NetworkFailure extends Failure {
  const NetworkFailure({required String message, String? code})
      : super(message: message, code: code);
}

class ValidationFailure extends Failure {
  const ValidationFailure({
    required String message,
    String? code,
    this.errors = const [],
  }) : super(message: message, code: code);

  final List<String> errors;
}

// ✅ Repository层的Either使用
class UserRepository {
  final ApiClient _apiClient;
  final UserLocalDataSource _localDataSource;

  UserRepository(this._apiClient, this._localDataSource);

  Future<Either<Failure, User>> getUser(String id) async {
    try {
      // 先尝试从本地获取
      final localUser = await _localDataSource.getUser(id);
      if (localUser != null) {
        return Right(localUser);
      }

      // 从远程API获取
      final response = await _apiClient.getUser(id);
      if (response.success && response.data != null) {
        // 缓存到本地
        await _localDataSource.saveUser(response.data!);
        return Right(response.data!);
      } else {
        return Left(NetworkFailure(message: response.message));
      }
    } on DioException catch (e) {
      return Left(NetworkFailure(
        message: '网络请求失败',
        code: e.response?.statusCode.toString(),
      ));
    } catch (e) {
      return Left(NetworkFailure(message: e.toString()));
    }
  }

  Future<Either<Failure, List<User>>> searchUsers(String query) async {
    if (query.trim().isEmpty) {
      return Left(ValidationFailure(
        message: '搜索关键词不能为空',
        code: 'EMPTY_QUERY',
      ));
    }

    try {
      final response = await _apiClient.searchUsers(query);
      return Right(response.data ?? []);
    } catch (e) {
      return Left(NetworkFailure(message: e.toString()));
    }
  }
}

// ✅ UseCase层的Either链式操作
class GetUserProfileUseCase {
  final UserRepository _userRepository;
  final PreferencesRepository _preferencesRepository;

  GetUserProfileUseCase(this._userRepository, this._preferencesRepository);

  Future<Either<Failure, UserProfile>> execute(String userId) async {
    // 链式Either操作
    final userResult = await _userRepository.getUser(userId);

    return userResult.fold(
      // 左侧：错误处理
      (failure) => Left(failure),
      // 右侧：成功处理
      (user) async {
        final preferencesResult = await _preferencesRepository.getUserPreferences(userId);

        return preferencesResult.fold(
          (failure) => Left(failure),
          (preferences) => Right(UserProfile(
            user: user,
            preferences: preferences,
          )),
        );
      },
    );
  }
}

// ✅ BLoC层的Either处理
class UserBloc extends Bloc<UserEvent, UserState> {
  final GetUserProfileUseCase _getUserProfileUseCase;

  UserBloc(this._getUserProfileUseCase) : super(UserState.initial()) {
    on<UserProfileLoaded>(_onUserProfileLoaded);
  }

  Future<void> _onUserProfileLoaded(
    UserProfileLoaded event,
    Emitter<UserState> emit,
  ) async {
    emit(UserState.loading());

    final result = await _getUserProfileUseCase.execute(event.userId);

    result.fold(
      // 错误处理
      (failure) => emit(UserState.error(message: failure.message)),
      // 成功处理
      (userProfile) => emit(UserState.loaded(userProfile: userProfile)),
    );
  }
}
```

#### 函数组合与高阶函数

```dart
// ✅ 项目中的函数组合模式
class DataTransformationService {
  // 函数组合：数据处理管道
  Either<Failure, List<OrderSummary>> processOrders(List<Order> orders) {
    return Right(orders)
        .map(_filterActiveOrders)
        .map(_sortByDate)
        .map(_transformToSummary)
        .map(_applyBusinessRules);
  }

  List<Order> _filterActiveOrders(List<Order> orders) {
    return orders.where((order) => order.isActive).toList();
  }

  List<Order> _sortByDate(List<Order> orders) {
    return orders..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  List<OrderSummary> _transformToSummary(List<Order> orders) {
    return orders.map((order) => OrderSummary.fromOrder(order)).toList();
  }

  List<OrderSummary> _applyBusinessRules(List<OrderSummary> summaries) {
    return summaries.map((summary) {
      // 应用业务规则
      if (summary.total > 1000) {
        return summary.copyWith(isVipOrder: true);
      }
      return summary;
    }).toList();
  }
}

// ✅ 高阶函数的应用
class ValidationService {
  // 验证器组合
  Either<ValidationFailure, T> validate<T>(
    T value,
    List<Either<ValidationFailure, T> Function(T)> validators,
  ) {
    for (final validator in validators) {
      final result = validator(value);
      if (result.isLeft()) {
        return result;
      }
    }
    return Right(value);
  }

  // 具体验证器
  Either<ValidationFailure, String> validateEmail(String email) {
    if (email.isEmpty) {
      return Left(ValidationFailure(message: '邮箱不能为空'));
    }
    if (!email.contains('@')) {
      return Left(ValidationFailure(message: '邮箱格式不正确'));
    }
    return Right(email);
  }

  Either<ValidationFailure, String> validatePassword(String password) {
    if (password.length < 8) {
      return Left(ValidationFailure(message: '密码长度不能少于8位'));
    }
    return Right(password);
  }

  // 使用验证器组合
  Either<ValidationFailure, LoginRequest> validateLoginRequest(
    String email,
    String password,
  ) {
    final emailResult = validate(email, [validateEmail]);
    if (emailResult.isLeft()) return emailResult.cast();

    final passwordResult = validate(password, [validatePassword]);
    if (passwordResult.isLeft()) return passwordResult.cast();

    return Right(LoginRequest(email: email, password: password));
  }
}
```
```
```

**Dart vs 其他语言**

- **vs JavaScript**：Dart天生支持类型检查，写错代码编译器会立即告诉你，而不是等到运行时才发现
- **vs Java**：Dart语法更简洁，写同样的功能代码量少30%
- **vs Python**：Dart既保持了Python的易读性，又拥有C++的运行效率

**现实案例**
阿里巴巴的闲鱼App，用Dart重构后：

- 崩溃率从0.8%降到0.1%
- 开发效率提升40%
- 包体积减少20%


---

## 第二章：变量与数据类型 - 程序世界的基石

### 🧱 什么是变量？

想象变量就像一个贴标签的盒子，我们把数据放进盒子，给盒子贴个名字的标签，以后就能通过这个名字找到数据。

```dart
// 生活中的例子
var 我的钱包 = 1000;        // 这是一个装钱的盒子
var 我的名字 = "张三";     // 这是一个装名字的盒子
var 是否成年 = true;       // 这是一个装真假的盒子

// 专业解释
var money = 1000;        // 自动推断为int类型
String name = "张三";      // 明确指定为字符串类型
bool isAdult = true;     // 明确指定为布尔类型
```

### 🔍 数据类型详解

#### 1. 数字类型（Number）

**int - 整数**

```dart
int 年龄 = 25;
int 分数 = -100;
int 大数 = 9223372036854775807;  // Dart的int最大到9百万万亿

// 实际应用
int 商品数量 = 5;
int 用户积分 = 1250;
```

**double - 小数**

```dart
double 价格 = 19.99;
double 体重 = 65.5;
double π = 3.141592653589793;

// 金融计算（避免精度问题）
double 商品总价 = 19.99 * 3;  // 59.970000000000006
// 正确做法：使用toStringAsFixed(2)保留2位小数
String 显示价格 = 商品总价.toStringAsFixed(2);  // "59.97"
```

#### 2. 字符串类型（String）

字符串就像一串字符的项链，每个字符都是项链上的一颗珠子。

```dart
// 单引号 vs 双引号（效果相同）
String 姓名1 = '张三';
String 姓名2 = "李四";

// 字符串插值 - 把变量塞进字符串
String 自我介绍 = "大家好，我是$name，今年$年龄岁";
// 等价于："大家好，我是张三，今年25岁"

// 多行字符串 - 像写诗一样写字符串
String 长文本 = '''
这是一个
可以换行的
多行字符串
'''.trim();  // trim()去掉首尾空格

// 字符串拼接
String 全名 = "张" + "三";  // "张三"
String 完整地址 = "北京市".concat("朝阳区");  // "北京市朝阳区"

// 字符串操作大全
String 邮箱 = "<EMAIL>";
print(邮箱.toUpperCase());      // "<EMAIL>"
print(邮箱.contains("@"));      // true，检查是否包含@符号
print(邮箱.replaceAll("@", "_"));  // "user_example.com"
print(邮箱.split("@"));         // ["user", "example.com"] 拆分成列表
```

#### 3. 布尔类型（bool）

布尔值只有两个：真（true）和假（false），就像开关一样。

```dart
bool 是否登录 = false;
bool 是否会员 = true;
bool 是否成年 = 年龄 >= 18;

// 实际应用
if (是否登录) {
  print("欢迎回来！");
} else {
  print("请先登录");
}
```

### 🎯 变量声明的三种方式

#### 1. var - 智能推断类型

```dart
var 名字 = "王五";      // Dart自动识别为String类型
var 年龄 = 30;          // Dart自动识别为int类型
var 价格 = 99.99;       // Dart自动识别为double类型

// 错误示例
var 测试变量 = "文本";
测试变量 = 123;  // ❌ 错误！不能从String变成int
```

#### 2. dynamic - 动态类型（谨慎使用）

```dart
dynamic 万能变量 = "可以是字符串";
万能变量 = 123;        // ✅ 允许，因为dynamic可以变成任何类型
万能变量 = true;       // ✅ 允许

// 实际场景：处理JSON数据时很有用
Map<String, dynamic> 用户数据 = {
  "name": "张三",
  "age": 25,
  "isVip": true,
  "scores": [100, 95, 88]
};
```

#### 3. final vs const - 不可变变量

**final - 运行时常量**

```dart
final 当前时间 = DateTime.now();  // 运行时确定值，之后不能改
final 用户配置 = loadUserConfig(); // 从文件加载，加载后不能改
```

**const - 编译时常量**

```dart
const 圆周率 = 3.14159;        // 编译时就知道值
const 最大重试次数 = 3;         // 永远不会变的值
const 默认头像 = "assets/default_avatar.png";

// const创建的对象也是不可变的
const 默认用户 = User(name: "游客", age: 0);  // User对象也不能改
```


---

## 第三章：函数 - 代码的乐高积木

### 🔧 函数的本质

函数就像工厂里的机器：你给它原材料（参数），它按照固定流程（函数体）加工，最后产出产品（返回值）。

### 基础函数定义

```dart
// 最简单的函数 - 打招呼
String 打招呼(String 名字) {
  return "你好，$名字！";
}

// 使用函数
var 问候语 = 打招呼("小明");  // "你好，小明！"

// 实际应用：计算商品总价
double 计算总价(double 单价, int 数量) {
  return 单价 * 数量;
}

var 总价 = 计算总价(19.99, 3);  // 59.97
```

### 箭头函数 - 一行搞定

当函数体只有一行时，可以用箭头函数让代码更简洁：

```dart
// 传统写法
int 平方(int 数字) {
  return 数字 * 数字;
}

// 箭头函数写法
int 平方简写(int 数字) => 数字 * 数字;

// 多个例子
bool 是否成年(int 年龄) => 年龄 >= 18;
String 生成订单号() => "ORDER_${DateTime.now().millisecondsSinceEpoch}";
```

### 参数的高级玩法

#### 1. 可选参数 - 灵活调用

```dart
// 位置可选参数（用[]表示）
String 创建用户(String 姓名, [String? 邮箱, int? 年龄]) {
  var 信息 = "用户：$姓名";
  if (邮箱 != null) 信息 += "，邮箱：$邮箱";
  if (年龄 != null) 信息 += "，年龄：$年龄岁";
  return 信息;
}

// 使用方式
createUser("张三");                    // "用户：张三"
createUser("李四", "<EMAIL>");  // "用户：李四，邮箱：<EMAIL>"
createUser("王五", "<EMAIL>", 25);  // 全部参数

// 命名可选参数（用{}表示，更直观）
String 创建商品({
  required String 名称,
  double 价格 = 0.0,
  String 描述 = "暂无描述",
  bool 是否上架 = true,
}) {
  return "商品：$名称，价格：¥${价格.toStringAsFixed(2)}，$描述";
}

// 使用方式（参数名一目了然）
创建商品(名称: "iPhone 15", 价格: 5999.0);
创建商品(
  名称: "MacBook",
  价格: 9999.0,
  描述: "轻薄便携的笔记本电脑",
  是否上架: false,
);
```

#### 2. 默认参数值

```dart
// 设置默认头像
String 获取用户头像(String 用户名, {String 默认头像 = "assets/default.png"}) {
  var 头像路径 = "assets/$用户名.png";
  // 检查头像文件是否存在
  return 文件存在(头像路径) ? 头像路径 : 默认头像;
}

// 使用
获取用户头像("张三");                     // 使用默认头像
获取用户头像("李四", 默认头像: "assets/cat.png");  // 覆盖默认值
```

### 一等公民的函数

在Dart中，函数可以像变量一样被传递，这就是"一等公民"的含义。

```dart
// 把函数存到变量里
var 数学运算 = 计算总价;  // 注意没有括号！
print(数学运算(10, 5));     // 50

// 函数作为参数传递
List<int> 处理列表(List<int> 列表, int 函数(int)) {
  return 列表.map(函数).toList();
}

// 使用
var 数字 = [1, 2, 3, 4, 5];
var 平方结果 = 处理列表(数字, 平方);  // [1, 4, 9, 16, 25]

// 匿名函数（lambda）
数字.forEach((数字) {
  print("当前数字：$数字");
});
```


---

## 第四章：面向对象 - 用代码描述现实世界

### 🏗️ 类的概念

类就像一个蓝图（模板），描述了一类事物的共同特征和行为。对象是根据这个蓝图创建的具体实例。

```dart
// 现实类比：
// 类 = 汽车设计图纸
// 对象 = 根据图纸造出来的具体汽车

// 定义一个用户类（蓝图）
class 用户 {
  // 属性（特征）
  String 姓名;
  int 年龄;
  String 邮箱;

  // 构造函数（创建对象的方法）
  用户(this.姓名, this.年龄, this.邮箱);

  // 方法（行为）
  void 打招呼() {
    print("你好，我是$姓名，今年$年龄岁");
  }

  void 过生日() {
    年龄++;
    print("$姓名过生日了，现在$年龄岁！");
  }
}

// 使用类创建对象
var 张三 = 用户("张三", 25, "<EMAIL>");
张三.打招呼();    // "你好，我是张三，今年25岁"
张三.过生日();    // "张三过生日了，现在26岁！"
```

### 构造函数详解

#### 1. 标准构造函数

```dart
class 商品 {
  String 名称;
  double 价格;
  String 分类;

  // 传统写法
  商品(String 名称, double 价格, String 分类) {
    this.名称 = 名称;
    this.价格 = 价格;
    this.分类 = 分类;
  }

  // Dart简写（推荐）
  商品(this.名称, this.价格, this.分类);
}

var 手机 = 商品("iPhone 15", 5999.0, "电子产品");
```

#### 2. 命名构造函数

```dart
class 订单 {
  String 订单号;
  DateTime 创建时间;
  List<String> 商品列表;

  // 主构造函数
  订单(this.订单号, this.商品列表) : 创建时间 = DateTime.now();

  // 命名构造函数 - 空订单
  订单.空订单() : this("EMPTY_${DateTime.now().millisecondsSinceEpoch}", []);

  // 命名构造函数 - 从JSON创建
  订单.从JSON(Map<String, dynamic> json)
    : 订单号 = json['id'],
      创建时间 = DateTime.parse(json['createdAt']),
      商品列表 = List<String>.from(json['items']);
}

// 使用不同构造函数
var 新订单 = 订单("ORD-001", ["iPhone", "AirPods"]);
var 空订单 = 订单.空订单();
var 网络订单 = 订单.从JSON({"id": "ORD-002", "createdAt": "2024-01-01", "items": ["MacBook"]});
```

#### 3. 工厂构造函数

```dart
class 数据库连接 {
  static 数据库连接? _实例;

  // 私有构造函数
  数据库连接._();

  // 工厂构造函数 - 保证单例
  factory 数据库连接() {
    return _实例 ??= 数据库连接._();
  }
}

// 使用：不管new多少次，都是同一个对象
var 连接1 = 数据库连接();
var 连接2 = 数据库连接();
print(连接1 == 连接2);  // true，是同一个对象
```

### 继承 - 代码复用的艺术

```dart
// 基类：动物（通用特征）
class 动物 {
  String 名称;
  int 年龄;

  动物(this.名称, this.年龄);

  void 发出声音() {
    print("$名称发出了声音");
  }

  void 获取信息() {
    print("名称：$名称，年龄：$年龄岁");
  }
}

// 子类：狗（继承动物的特征，并添加自己的特性）
class 狗 extends 动物 {
  String 品种;

  // 调用父类构造函数
  狗(String 名称, int 年龄, this.品种) : super(名称, 年龄);

  // 重写父类方法
  @override
  void 发出声音() {
    print("$名称（$品种）汪汪叫！");
  }

  // 子类特有的方法
  void 摇尾巴() {
    print("$名称开心地摇尾巴");
  }
}

// 子类：猫
class 猫 extends 动物 {
  int 抓老鼠数量;

  猫(String 名称, int 年龄, this.抓老鼠数量) : super(名称, 年龄);

  @override
  void 发出声音() {
    print("$名称喵喵叫，已经抓了$抓老鼠数量只老鼠！");
  }

  void 抓老鼠() {
    抓老鼠数量++;
    print("$名称又抓了一只老鼠！总数：$抓老鼠数量");
  }
}

// 使用多态
void 描述动物(动物 某动物) {
  某动物.获取信息();
  某动物.发出声音();
}

void main() {
  var 小黑 = 狗("小黑", 3, "拉布拉多");
  var 小花 = 猫("小花", 2, 5);

  描述动物(小黑);  // 会自动调用狗的方法
  描述动物(小花);  // 会自动调用猫的方法
}
```

### 抽象类与接口

```dart
// 抽象类 - 定义规范，不具体实现
abstract class 支付接口 {
  // 抽象方法 - 子类必须实现
  Future<bool> 支付(double 金额);
  Future<bool> 退款(String 订单号);

  // 普通方法 - 可以继承使用
  void 记录日志(String 操作) {
    print("[支付日志] $操作 - ${DateTime.now()}");
  }
}

// 具体实现：微信支付
class 微信支付 extends 支付接口 {
  @override
  Future<bool> 支付(double 金额) async {
    print("正在使用微信支付 $金额 元...");
    await Future.delayed(Duration(seconds: 1));
    return true;  // 模拟支付成功
  }

  @override
  Future<bool> 退款(String 订单号) async {
    print("正在处理微信退款，订单号：$订单号");
    return true;
  }
}

// 具体实现：支付宝支付
class 支付宝支付 extends 支付接口 {
  @override
  Future<bool> 支付(double 金额) async {
    print("正在使用支付宝支付 $金额 元...");
    return true;
  }

  @override
  Future<bool> 退款(String 订单号) async {
    print("正在处理支付宝退款，订单号：$订单号");
    return true;
  }
}

// 使用 - 面向接口编程
class 订单服务 {
  final 支付接口 _支付器;

  订单服务(this._支付器);

  Future<void> 处理订单(double 金额) async {
    _支付器.记录日志("开始支付 $金额 元");

    var 成功 = await _支付器.支付(金额);
    if (成功) {
      print("订单支付成功！");
    }
  }
}

// 使用不同支付方式
void main() {
  var 微信订单 = 订单服务(微信支付());
  var 支付宝订单 = 订单服务(支付宝支付());

  微信订单.处理订单(100);
  支付宝订单.处理订单(200);
}
```

### Mixins - 功能组合的艺术

```dart
// 定义mixin - 像乐高积木一样的功能块
mixin 日志功能 {
  void 打印日志(String 消息) {
    print("[${DateTime.now()}] $消息");
  }
}

mixin 缓存功能 {
  final Map<String, dynamic> _缓存 = {};

  void 保存缓存(String 键, dynamic 值) {
    _缓存[键] = 值;
    print("已缓存：$键 = $值");
  }

  dynamic 读取缓存(String 键) => _缓存[键];
}

// 使用mixin组合功能
class 用户服务 with 日志功能, 缓存功能 {
  void 获取用户信息(String 用户ID) {
    打印日志("开始获取用户信息：$用户ID");

    var 缓存数据 = 读取缓存("user_$用户ID");
    if (缓存数据 != null) {
      打印日志("从缓存获取成功");
      return 缓存数据;
    }

    // 模拟从数据库获取
    var 用户信息 = {"id": 用户ID, "name": "用户$用户ID"};
    保存缓存("user_$用户ID", 用户信息);
    return 用户信息;
  }
}

// 使用
var 服务 = 用户服务();
服务.获取用户信息("123");
```


---

## 第五章：集合类型 - 数据的容器

### 📦 List - 有序的队列

List就像一个排好队的队伍，每个人都有固定的位置（索引，从0开始）。

```dart
// 创建List的多种方式
var 水果列表 = ["苹果", "香蕉", "橙子"];  // 自动推断为List<String>
List<int> 成绩 = [95, 87, 92, 78];      // 明确指定类型
List<dynamic> 混合列表 = [1, "文本", true];  // 可以放任何类型（不推荐）

// 访问元素
print(水果列表[0]);   // "苹果" - 第一个元素
print(水果列表[2]);   // "橙子" - 第三个元素
print(水果列表.last); // "橙子" - 最后一个元素

// 修改元素
水果列表[1] = "草莓";  // 把香蕉换成草莓
水果列表.add("葡萄");  // 在末尾添加
水果列表.insert(0, "榴莲");  // 在开头插入

// 实际案例：购物车
class 购物车 {
  List<String> 商品列表 = [];

  void 添加商品(String 商品) {
    if (!商品列表.contains(商品)) {
      商品列表.add(商品);
      print("已添加 $商品 到购物车");
    }
  }

  void 移除商品(String 商品) {
    商品列表.remove(商品);
    print("已移除 $商品");
  }

  int 商品数量() => 商品列表.length;

  void 显示所有商品() {
    print("购物车中有：");
    商品列表.asMap().forEach((索引, 商品) {
      print("${索引 + 1}. $商品");
    });
  }
}
```

### List的高级操作

```dart
var 数字 = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];

// map - 把每个元素变成新东西
var 平方 = 数字.map((n) => n * n).toList();  // [1, 4, 9, 16, 25, 36, 49, 64, 81, 100]

// where - 筛选符合条件的元素
var 偶数 = 数字.where((n) => n % 2 == 0).toList();  // [2, 4, 6, 8, 10]

// reduce - 把所有元素合成一个结果
var 总和 = 数字.reduce((a, b) => a + b);  // 55

// 实际应用：订单处理
class 订单处理器 {
  List<Map<String, dynamic>> 订单 = [
    {"id": "001", "金额": 100, "状态": "已支付"},
    {"id": "002", "金额": 200, "状态": "待支付"},
    {"id": "003", "金额": 150, "状态": "已支付"},
  ];

  // 获取所有已支付订单的总金额
  double 获取已支付总金额() {
    return 订单
        .where((订单) => 订单["状态"] == "已支付")
        .map((订单) => 订单["金额"])
        .reduce((a, b) => a + b)
        .toDouble();
  }

  // 获取订单ID列表
  List<String> 获取订单ID列表() => 订单.map((订单) => 订单["id"]).toList();
}
```

### 📖 Map - 键值对的字典

Map就像一本字典，通过"键"（单词）可以快速找到对应的"值"（解释）。

```dart
// 创建Map的多种方式
var 用户 = {
  "姓名": "张三",
  "年龄": 25,
  "邮箱": "<EMAIL>",
  "是否会员": true,
};  // 自动推断为Map<String, dynamic>

Map<String, String> 配置 = {
  "主题": "深色",
  "语言": "中文",
  "字体大小": "中",
};

// 访问和修改
print(用户["姓名"]);        // "张三"
用户["年龄"] = 26;        // 修改年龄
用户["城市"] = "北京";      // 添加新键值对

// 检查键是否存在
if (用户.containsKey("电话")) {
  print("用户有电话：${用户["电话"]}");
} else {
  print("用户没有填写电话");
}

// 实际应用：商品信息
class 商品管理器 {
  Map<String, Map<String, dynamic>> 商品库 = {
    "iphone15": {
      "名称": "iPhone 15",
      "价格": 5999,
      "库存": 100,
      "颜色": ["黑色", "白色", "蓝色"],
    },
    "macbook": {
      "名称": "MacBook Air",
      "价格": 8999,
      "库存": 50,
      "颜色": ["银色", "深空灰"],
    },
  };

  void 显示商品信息(String 商品ID) {
    var 商品 = 商品库[商品ID];
    if (商品 != null) {
      print("商品：${商品["名称"]}");
      print("价格：¥${商品["价格"]}");
      print("库存：${商品["库存"]}件");
    }
  }

  void 更新价格(String 商品ID, double 新价格) {
    if (商品库.containsKey(商品ID)) {
      商品库[商品ID]!["价格"] = 新价格;
      print("已更新 $商品ID 的价格为 ¥$新价格");
    }
  }
}
```

### 🎯 Set - 唯一的集合

Set就像一个没有重复的名单，确保每个元素都是唯一的。

```dart
// 创建Set
var 唯一数字 = {1, 2, 3, 3, 4, 4, 5};  // 自动去重：{1, 2, 3, 4, 5}
Set<String> 标签 = {"Flutter", "Dart", "Mobile"};

// 添加元素
标签.add("UI");        // 添加成功
标签.add("Flutter");   // 添加失败，因为已经存在

// 集合运算
var 前端技能 = {"HTML", "CSS", "JavaScript"};
var 后端技能 = {"Python", "Java", "JavaScript"};

var 全栈技能 = 前端技能.union(后端技能);           // 并集：所有技能
var 共同技能 = 前端技能.intersection(后端技能);     // 交集：共同有的
var 独有技能 = 前端技能.difference(后端技能);       // 差集：前端独有

// 实际应用：用户权限管理
class 权限管理器 {
  Set<String> 管理员权限 = {"删除用户", "修改设置", "查看报表"};
  Set<String> 普通用户权限 = {"查看内容", "发表评论"};

  bool 检查权限(String 用户类型, String 操作) {
    switch (用户类型) {
      case "管理员":
        return 管理员权限.contains(操作);
      case "普通用户":
        return 普通用户权限.contains(操作);
      default:
        return false;
    }
  }

  void 添加权限(String 用户类型, String 新权限) {
    if (用户类型 == "管理员") {
      管理员权限.add(新权限);
    }
  }
}
```


---

## 第四章：控制流程

### 🔄 条件判断

生活中我们经常需要做选择：如果下雨就带伞，如果没下雨就不带。编程中的条件判断也是一样的道理。

#### if-else 语句

```dart
// 基础条件判断
int 年龄 = 20;

if (年龄 >= 18) {
  print("你已经成年了");
} else {
  print("你还未成年");
}

// 多重条件判断
int 分数 = 85;

if (分数 >= 90) {
  print("优秀！");
} else if (分数 >= 80) {
  print("良好！");
} else if (分数 >= 60) {
  print("及格！");
} else {
  print("需要努力！");
}

// 实际应用：用户权限检查
void 检查用户权限(String 用户类型, String 操作) {
  if (用户类型 == "管理员") {
    print("管理员可以执行任何操作：$操作");
  } else if (用户类型 == "会员" && 操作 != "删除") {
    print("会员可以执行：$操作");
  } else if (用户类型 == "游客" && 操作 == "浏览") {
    print("游客只能浏览");
  } else {
    print("权限不足，无法执行：$操作");
  }
}
```

#### 三元运算符

```dart
// 简洁的条件赋值
int 年龄 = 20;
String 状态 = 年龄 >= 18 ? "成年人" : "未成年人";

// 实际应用：价格计算
double 计算折扣价格(double 原价, bool 是否会员) {
  return 是否会员 ? 原价 * 0.8 : 原价;
}

var 商品价格 = 100.0;
var 最终价格 = 计算折扣价格(商品价格, true);  // 80.0
```

### 🔁 循环结构

#### for 循环 - 已知次数的重复

```dart
// 基础for循环
for (int i = 0; i < 5; i++) {
  print("第${i + 1}次循环");
}

// 遍历列表
var 水果 = ["苹果", "香蕉", "橙子"];
for (int i = 0; i < 水果.length; i++) {
  print("第${i + 1}个水果：${水果[i]}");
}

// for-in 循环（更简洁）
for (var 水果名 in 水果) {
  print("我喜欢吃$水果名");
}

// 实际应用：批量处理数据
void 批量发送邮件(List<String> 邮箱列表) {
  for (var 邮箱 in 邮箱列表) {
    if (邮箱.contains("@")) {
      print("发送邮件到：$邮箱");
      // 发送邮件的逻辑
    } else {
      print("无效邮箱：$邮箱");
    }
  }
}
```

#### while 循环 - 条件满足时重复

```dart
// 基础while循环
int 计数 = 0;
while (计数 < 3) {
  print("计数：$计数");
  计数++;
}

// 实际应用：用户输入验证
void 获取有效密码() {
  String? 密码;

  while (密码 == null || 密码.length < 6) {
    print("请输入至少6位的密码：");
    密码 = "123";  // 模拟用户输入

    if (密码.length < 6) {
      print("密码长度不够，请重新输入");
      密码 = null;  // 重置，继续循环
    }
  }

  print("密码设置成功！");
}
```

#### 🎯 switch 语句 - 多分支选择

```dart
// 传统switch
void 处理用户操作(String 操作) {
  switch (操作) {
    case "登录":
      print("执行登录操作");
      break;
    case "注册":
      print("执行注册操作");
      break;
    case "忘记密码":
      print("执行密码重置");
      break;
    default:
      print("未知操作：$操作");
  }
}

// Dart 3.0+ 的新式switch表达式
String 获取星期描述(int 星期) {
  return switch (星期) {
    1 => "周一，新的开始！",
    2 => "周二，继续努力！",
    3 => "周三，过半了！",
    4 => "周四，快到周末了！",
    5 => "周五，明天休息！",
    6 || 7 => "周末，好好休息！",
    _ => "无效的星期数",
  };
}

// 实际应用：订单状态处理
class 订单处理器 {
  String 获取状态描述(String 状态) {
    return switch (状态) {
      "pending" => "订单处理中...",
      "paid" => "支付成功，准备发货",
      "shipped" => "商品已发货，请注意查收",
      "delivered" => "订单已完成",
      "cancelled" => "订单已取消",
      _ => "未知订单状态",
    };
  }
}
```


---

## 第十一章：空安全 - 消灭空指针异常

### ❓ 什么是空指针异常？

想象你去超市拿牛奶，结果发现货架是空的（null），你还硬要拿，结果会怎样？程序就会崩溃！这就是空指针异常。

### 🛡️ Dart的空安全机制

#### 1. 可空类型 vs 非空类型

```dart
// 非空类型（默认）- 绝对不能为空
String 用户名 = "张三";  // 这个变量永远有值
// 用户名 = null;      // ❌ 编译错误！

// 可空类型（加?表示）- 可以为空
String? 昵称;           // 这个变量可以为null
昵称 = "小张";          // ✅ 允许
昵称 = null;            // ✅ 也允许

// 实际场景对比
class 用户资料 {
  String 姓名;          // 必须有姓名，不能为空
  String? 中间名;       // 可能没有中间名
  String? 邮箱;         // 可能没有邮箱

  用户资料(this.姓名, [this.中间名, this.邮箱]);
}
```

#### 2. 安全访问操作符

```dart
// 安全调用（?.）- 如果是null就跳过
String? 用户邮箱;
print(用户邮箱?.length);  // 如果邮箱是null，返回null，不报错

// 空值合并（??）- 如果是null就用默认值
String 显示邮箱 = 用户邮箱 ?? "未设置邮箱";

// 空断言（!）- 我确定不是null（谨慎使用）
String 邮箱 = 用户邮箱!;  // 如果真的是null，运行时会报错

// 实际应用
class 用户验证器 {
  String? 验证邮箱(String? 邮箱) {
    if (邮箱 == null || 邮箱.isEmpty) {
      return "邮箱不能为空";
    }

    if (!邮箱.contains("@")) {
      return "邮箱格式不正确";
    }

    return null;  // 返回null表示验证通过
  }

  void 发送邮件(String? 邮箱) {
    var 有效邮箱 = 邮箱 ?? "<EMAIL>";
    print("发送邮件到：$有效邮箱");
  }
}
```

#### 3. 延迟初始化（late）

```dart
// 场景：某些属性需要在对象创建后才能确定
class 用户设置 {
  late String 主题颜色;  // 稍后会初始化

  void 加载设置() {
    // 从配置文件读取
    主题颜色 = 读取配置("theme") ?? "蓝色";
  }

  String 获取主题() {
    if (!主题颜色.isInitialized) {
      throw Exception("请先调用loadSettings()");
    }
    return 主题颜色;
  }
}

// 实际应用：数据库连接
class 数据库服务 {
  late final 数据库连接 _连接;

  数据库服务() {
    _连接 = 数据库连接.创建连接();  // 延迟到构造函数中初始化
  }
}
```


---

## 第十二章：异步编程 - 不阻塞的世界

### 🔄 什么是异步？

想象你在餐厅点餐：

- **同步**：点餐后一直站着等，不能做其他事情
- **异步**：点餐后坐着玩手机，等餐好了服务员叫你

### Future - 未来的承诺

Future就像一个"未来的承诺"：我现在开始处理，处理完了告诉你结果。

```dart
// 同步 vs 异步对比

// 同步操作 - 会卡住程序
String 读取大文件() {
  sleep(Duration(seconds: 3));  // 程序卡住3秒
  return "文件内容...";
}

// 异步操作 - 不会卡住程序
Future<String> 读取大文件异步() async {
  await Future.delayed(Duration(seconds: 3));  // 等待但不卡住
  return "文件内容...";
}

// 使用异步函数
void main() async {
  print("开始读取文件...");

  var 内容 = await 读取大文件异步();  // 等待结果
  print("文件读取完成：$内容");

  print("这行代码会在文件读取完成后才执行");
}
```

### 实际案例：网络请求

```dart
// 模拟网络API
class 用户API {
  // 获取用户信息的异步方法
  static Future<Map<String, dynamic>> 获取用户信息(String 用户ID) async {
    print("正在请求用户 $用户ID 的信息...");

    // 模拟网络延迟
    await Future.delayed(Duration(seconds: 2));

    // 模拟随机成功/失败
    if (Random().nextBool()) {
      return {
        "id": 用户ID,
        "姓名": "用户$用户ID",
        "邮箱": "user$用户**************",
        "注册时间": "2024-01-01"
      };
    } else {
      throw Exception("网络连接失败");
    }
  }
}

// 错误处理的正确姿势
Future<void> 安全获取用户信息(String 用户ID) async {
  try {
    print("开始获取用户信息...");

    var 用户信息 = await 用户API.获取用户信息(用户ID);

    print("获取成功：");
    print("姓名：${用户信息["姓名"]}");
    print("邮箱：${用户信息["邮箱"]}");

  } catch (错误) {
    print("获取失败：${错误.message}");

    // 可以在这里显示友好的错误提示给用户
    显示错误提示("获取用户信息失败，请检查网络连接");
  } finally {
    // 无论成功失败都会执行
    print("请求完成，隐藏加载动画");
  }
}

void 显示错误提示(String 消息) {
  print("🚨 $消息");
}
```

### Stream - 连续的数据流

Stream就像水龙头，可以持续不断地流出数据。

```dart
// 创建Stream - 模拟股票实时价格
Stream<double> 股票价格流(String 股票代码) async* {
  var 当前价格 = 100.0;

  while (true) {
    // 模拟价格变化（-5% 到 +5%）
    var 变化 = Random().nextDouble() * 10 - 5;
    当前价格 += 变化;

    yield 当前价格;  // 发送当前价格

    await Future.delayed(Duration(seconds: 1));
  }
}

// 监听Stream
void 监控股票价格() {
  var 订阅 = 股票价格流("AAPL").listen((价格) {
    print("🔄 当前价格：¥${价格.toStringAsFixed(2)}");

    if (价格 > 110) {
      print("🚀 价格创新高！考虑卖出");
    } else if (价格 < 90) {
      print("📉 价格较低，考虑买入");
    }
  });

  // 10秒后停止监听
  Future.delayed(Duration(seconds: 10), () {
    订阅.cancel();
    print("停止监控");
  });
}

// 实际应用：实时聊天消息
class 聊天室 {
  final StreamController<String> _消息控制器 = StreamController<String>();

  // 发送消息
  void 发送消息(String 消息) {
    _消息控制器.add("用户A: $消息");
  }

  // 接收消息
  Stream<String> 获取消息流() => _消息控制器.stream;

  void 关闭聊天室() {
    _消息Controller.close();
  }
}
```

### async/await 最佳实践

```dart
// 并行执行多个异步操作
Future<void> 并行加载数据() async {
  print("开始并行加载...");

  // 同时发起多个请求
  var 用户任务 = 用户API.获取用户信息("123");
  var 订单任务 = 订单API.获取订单列表("123");
  var 商品任务 = 商品API.获取推荐商品();

  // 等待所有任务完成
  var 结果 = await Future.wait([
    用户任务,
    订单任务,
    商品任务,
  ]);

  print("所有数据加载完成：");
  print("用户信息：${结果[0]}");
  print("订单数量：${结果[1].length}");
  print("推荐商品：${结果[2].take(3)}");
}

// 超时控制
Future<T> 带超时<T>(Future<T> 任务, {Duration 超时时间 = const Duration(seconds: 5)}) async {
  try {
    return await 任务.timeout(超时时间);
  } on TimeoutException {
    print("请求超时，请重试");
    throw Exception("请求超时");
  }
}

// 使用
await 带超时(用户API.获取用户信息("123"), 超时时间: Duration(seconds: 3));
```


---

## 第八章：泛型 - 类型安全的模板

### 🎯 什么是泛型？

泛型就像做蛋糕的模具，你可以用同一个模具做不同口味的蛋糕，但模具的形状（类型）是固定的。

### 泛型类

```dart
// 非泛型版本 - 代码重复
class 字符串盒子 {
  String 值;
  字符串盒子(this.值);
}

class 数字盒子 {
  int 值;
  数字盒子(this.值);
}

// 泛型版本 - 一个类解决所有类型
class 万能盒子<T> {
  T 值;
  万能盒子(this.值);

  T 获取值() => 值;
  void 设置值(T 新值) => 值 = 新值;
}

// 使用
var 文本盒子 = 万能盒子<String>("Hello");
var 数字盒子 = 万能盒子<int>(42);
var 用户盒子 = 万能盒子<用户>(用户("张三", 25));

print(文本盒子.获取值());  // "Hello"
print(数字盒子.获取值());  // 42
```

### 泛型在实际中的应用

```dart
// API响应的泛型封装
class API响应<T> {
  final T? 数据;
  final String? 错误消息;
  final int 状态码;

  API响应.成功(this.数据) : 错误消息 = null, 状态码 = 200;
  API响应.失败(this.错误消息) : 数据 = null, 状态码 = 400;

  bool 是否成功() => 错误消息 == null;
}

// 使用示例
Future<API响应<用户>> 获取用户信息(String 用户ID) async {
  try {
    var 用户 = await 用户API.获取用户信息(用户ID);
    return API响应<用户>.成功(用户);
  } catch (错误) {
    return API响应<用户>.失败("获取用户信息失败：${错误.message}");
  }
}

// 仓库模式的泛型实现
abstract class 仓库<T> {
  Future<T?> 通过ID查找(String id);
  Future<List<T>> 获取全部();
  Future<void> 保存(T 实体);
  Future<void> 删除(String id);
}

class 用户仓库 implements 仓库<用户> {
  @override
  Future<用户?> 通过ID查找(String id) async {
    // 从数据库查找用户
    return await 数据库.查询用户(id);
  }

  @override
  Future<List<用户>> 获取全部() async {
    return await 数据库.获取所有用户();
  }

  @override
  Future<void> 保存(用户 实体) async {
    await 数据库.保存用户(实体);
  }

  @override
  Future<void> 删除(String id) async {
    await 数据库.删除用户(id);
  }
}
```

### 泛型约束

```dart
// 限制只能是数字类型
class 计算器<T extends num> {
  T 加(T a, T b) => a + b;
  T 减(T a, T b) => a - b;
}

// 使用
var 整数计算器 = 计算器<int>();
print(整数计算器.加(5, 3));  // 8

var 小数计算器 = 计算器<double>();
print(小数计算器.加(5.5, 3.2));  // 8.7

// 不能这样用：
// var 文本计算器 = 计算器<String>();  // ❌ 编译错误，String不是num的子类
```


---

## 第十三章：Isolate并发编程

### 🧠 什么是Isolate？

想象你的应用是一家餐厅：

- **单线程（传统）**：只有一个厨师，做菜、收银、服务都是他一个人，客人多了就忙不过来
- **Isolate（Dart的方式）**：每个Isolate就像一个独立的厨师，有自己的厨房（内存空间），互不干扰，通过对讲机（消息传递）协作

### 🔄 Isolate vs 线程的区别

```dart
// 传统多线程模型（共享内存，容易出问题）
// Thread1: 修改共享变量 count = 5
// Thread2: 同时读取 count，可能读到不一致的值

// Dart的Isolate模型（隔离内存，消息传递）
// Isolate1: 有自己的 count = 5
// Isolate2: 有自己的 count = 10
// 通过消息传递进行通信，不会有数据竞争
```

### 🚀 基础Isolate使用

#### 1. Isolate.run() - 简单任务

```dart
import 'dart:isolate';
import 'dart:math';

// 简单的计算任务
Future<int> 简单计算() async {
  return await Isolate.run(() {
    // 这里的代码在新的isolate中运行
    var result = 0;
    for (int i = 0; i < 1000000; i++) {
      result += i;
    }
    return result;
  });
}

// 复杂数据处理
Future<List<int>> 处理大数据(List<int> 数据) async {
  return await Isolate.run(() {
    // 模拟耗时的数据处理
    return 数据.where((x) => x % 2 == 0)
              .map((x) => x * x)
              .toList();
  });
}

// 使用示例
void main() async {
  print("开始计算...");

  // 主isolate继续响应用户交互
  var 结果 = await 简单计算();
  print("计算结果：$结果");

  var 大数据 = List.generate(1000000, (i) => i);
  var 处理结果 = await 处理大数据(大数据);
  print("处理完成，结果数量：${处理结果.length}");
}
```

#### 2. Flutter中的compute()函数

```dart
import 'package:flutter/foundation.dart';
import 'dart:convert';

// JSON解析（在isolate中进行，不阻塞UI）
List<Photo> 解析照片JSON(String jsonString) {
  final parsed = jsonDecode(jsonString).cast<Map<String, dynamic>>();
  return parsed.map<Photo>((json) => Photo.fromJson(json)).toList();
}

class PhotoService {
  Future<List<Photo>> 加载照片() async {
    // 模拟从网络获取大量JSON数据
    String 大量JSON数据 = await 网络请求.获取照片数据();

    // 使用compute在隔离线程中解析，不阻塞UI
    List<Photo> 照片列表 = await compute(解析照片JSON, 大量JSON数据);

    return 照片列表;
  }
}

// 实际应用：图片压缩
Future<Uint8List> 压缩图片(Uint8List 原图数据) {
  return compute(_压缩图片逻辑, 原图数据);
}

Uint8List _压缩图片逻辑(Uint8List 数据) {
  // 复杂的图片压缩算法
  // 这里会在独立的isolate中运行，不影响UI流畅度
  return 压缩后的数据;
}
```

### 🔗 长生命周期Isolate通信

```dart
import 'dart:isolate';

// Isolate管理器
class IsolateManager {
  Isolate? _isolate;
  SendPort? _sendPort;
  ReceivePort? _receivePort;

  // 初始化长期运行的isolate
  Future<void> 初始化() async {
    _receivePort = ReceivePort();

    // 创建isolate
    _isolate = await Isolate.spawn(
      isolate入口函数,
      _receivePort!.sendPort,
    );

    // 等待isolate发送回它的SendPort
    _sendPort = await _receivePort!.first as SendPort;
    print("Isolate初始化完成");
  }

  // 发送任务到isolate
  Future<T> 执行任务<T>(String 任务类型, dynamic 参数) async {
    if (_sendPort == null) throw Exception("Isolate未初始化");

    // 创建响应端口
    final responsePort = ReceivePort();

    // 发送任务
    _sendPort!.send({
      'type': 任务类型,
      'data': 参数,
      'responsePort': responsePort.sendPort,
    });

    // 等待结果
    final result = await responsePort.first;
    return result as T;
  }

  // 清理资源
  void 销毁() {
    _isolate?.kill();
    _receivePort?.close();
  }
}

// Isolate入口函数
void isolate入口函数(SendPort mainSendPort) {
  // 创建接收端口
  final port = ReceivePort();

  // 告诉主isolate我们的SendPort
  mainSendPort.send(port.sendPort);

  // 监听任务
  port.listen((message) {
    final taskType = message['type'] as String;
    final data = message['data'];
    final responsePort = message['responsePort'] as SendPort;

    // 根据任务类型执行不同的处理
    switch (taskType) {
      case 'heavy_calculation':
        final result = 复杂计算(data);
        responsePort.send(result);
        break;

      case 'data_processing':
        final result = 数据处理(data);
        responsePort.send(result);
        break;

      default:
        responsePort.send('未知任务类型');
    }
  });
}

// 业务逻辑函数
int 复杂计算(int n) {
  var result = 0;
  for (int i = 0; i < n; i++) {
    result += i * i;
  }
  return result;
}

List<String> 数据处理(List<String> 数据) {
  return 数据.map((item) => item.toUpperCase()).toList();
}
```

### 📱 实际应用场景

#### 1. 大文件处理

```dart
class 文件处理器 {
  Future<String> 处理大文件(String 文件路径) async {
    return await compute(_处理文件, 文件路径);
  }
}

String _处理文件(String 路径) {
  // 读取大文件
  final file = File(路径);
  final content = file.readAsStringSync();

  // 复杂的文本处理
  final processed = content
      .split('\n')
      .where((line) => line.trim().isNotEmpty)
      .map((line) => line.toUpperCase())
      .join('\n');

  return processed;
}
```

#### 2. 加密解密操作

```dart
import 'dart:convert';
import 'dart:typed_data';

class 加密服务 {
  Future<String> 加密数据(String 明文, String 密钥) async {
    return await compute(_执行加密, {'data': 明文, 'key': 密钥});
  }

  Future<String> 解密数据(String 密文, String 密钥) async {
    return await compute(_执行解密, {'data': 密文, 'key': 密钥});
  }
}

String _执行加密(Map<String, String> params) {
  // 复杂的加密算法
  final data = params['data']!;
  final key = params['key']!;

  // 模拟耗时的加密过程
  var encrypted = '';
  for (int i = 0; i < data.length; i++) {
    encrypted += String.fromCharCode(
      data.codeUnitAt(i) ^ key.codeUnitAt(i % key.length)
    );
  }

  return base64Encode(utf8.encode(encrypted));
}

String _执行解密(Map<String, String> params) {
  // 解密逻辑
  final encryptedData = params['data']!;
  final key = params['key']!;

  final encrypted = utf8.decode(base64Decode(encryptedData));
  var decrypted = '';

  for (int i = 0; i < encrypted.length; i++) {
    decrypted += String.fromCharCode(
      encrypted.codeUnitAt(i) ^ key.codeUnitAt(i % key.length)
    );
  }

  return decrypted;
}
```

#### 3. 网络数据批处理

```dart
class 批处理服务 {
  final IsolateManager _isolateManager = IsolateManager();

  Future<void> 初始化() async {
    await _isolateManager.初始化();
  }

  Future<List<ProcessedData>> 批量处理数据(List<RawData> 原始数据) async {
    // 将大批量数据分块处理
    const 分块大小 = 1000;
    List<Future<List<ProcessedData>>> 任务列表 = [];

    for (int i = 0; i < 原始数据.length; i += 分块大小) {
      final 分块 = 原始数据.skip(i).take(分块大小).toList();

      final 任务 = _isolateManager.执行任务<List<ProcessedData>>(
        'batch_process',
        分块,
      );

      任务列表.add(任务);
    }

    // 等待所有分块处理完成
    final 结果列表 = await Future.wait(任务列表);

    // 合并结果
    return 结果列表.expand((list) => list).toList();
  }

  void 销毁() {
    _isolateManager.销毁();
  }
}
```

### ⚠️ 最佳实践与注意事项

#### 1. 何时使用Isolate

```dart
// ✅ 适合使用Isolate的场景
- JSON解析（大量数据）
- 图片/视频处理
- 文件压缩/解压
- 复杂计算（如算法、加密）
- 大量数据的排序/过滤

// ❌ 不适合使用Isolate的场景
- 简单的数学计算
- 少量数据处理
- UI相关操作
- 频繁的小任务
```

#### 2. 性能优化技巧

```dart
// 避免频繁创建isolate
class 优化的处理器 {
  static IsolateManager? _sharedIsolate;

  static Future<IsolateManager> 获取共享Isolate() async {
    if (_sharedIsolate == null) {
      _sharedIsolate = IsolateManager();
      await _sharedIsolate!.初始化();
    }
    return _sharedIsolate!;
  }
}

// 批量处理避免频繁通信
Future<List<Result>> 批量处理任务(List<Task> 任务列表) async {
  // ✅ 好的做法：批量发送
  return await compute(批量处理函数, 任务列表);

  // ❌ 避免的做法：逐个发送
  // for (final task in 任务列表) {
  //   await compute(单个处理函数, task);
  // }
}
```

#### 3. 错误处理

```dart
class 安全的Isolate服务 {
  Future<T?> 安全执行<T>(Future<T> Function() 任务) async {
    try {
      return await 任务();
    } on IsolateSpawnException catch (e) {
      print('Isolate启动失败: $e');
      return null;
    } on TimeoutException catch (e) {
      print('Isolate任务超时: $e');
      return null;
    } catch (e) {
      print('Isolate执行错误: $e');
      return null;
    }
  }
}
```

### 🎯 实战案例：图片批处理应用

```dart
class 图片批处理应用 {
  final IsolateManager _isolate = IsolateManager();

  Future<void> 初始化() async {
    await _isolate.初始化();
  }

  Future<List<Uint8List>> 批量压缩图片(List<String> 图片路径列表) async {
    List<Future<Uint8List>> 压缩任务 = [];

    for (String 路径 in 图片路径列表) {
      final 任务 = _isolate.执行任务<Uint8List>('compress_image', 路径);
      压缩任务.add(任务);
    }

    // 显示进度
    int 完成数量 = 0;
    List<Uint8List> 结果 = [];

    for (final 任务 in 压缩任务) {
      final 压缩结果 = await 任务;
      结果.add(压缩结果);
      完成数量++;

      print('进度: $完成数量/${图片路径列表.length}');
    }

    return 结果;
  }
}
```


---

## 第十四章：扩展方法 - 给现有类加功能

### 🔧 什么是扩展方法？

想象你买了一个手机，后来发现需要一个新的功能。扩展方法就像给手机贴个智能贴纸，让它拥有新能力，而不需要拆开手机改装。

### 基础扩展

```dart
// 给String添加扩展 - 邮箱验证
extension 字符串邮箱验证 on String {
  bool 是有效邮箱() {
    return RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(this);
  }

  String 首字母大写() {
    if (isEmpty) return this;
    return this[0].toUpperCase() + substring(1);
  }
}

// 使用扩展方法
var 邮箱 = "<EMAIL>";
print(邮箱.是有效邮箱());      // true
print("hello world".首字母大写());  // "Hello world"

// 给数字添加扩展
extension 数字格式化 on num {
  String 货币格式() => "¥${toStringAsFixed(2)}";
  String 百分比格式() => "${(this * 100).toStringAsFixed(1)}%";
}

var 价格 = 19.99;
print(价格.货币格式());       // "¥19.99"
print(0.85.百分比格式());    // "85.0%"
```

### 实际应用扩展

```dart
// 给List添加分页功能
extension 列表分页<T> on List<T> {
  List<T> 获取分页(int 页码, int 每页数量) {
    var 开始索引 = (页码 - 1) * 每页数量;
    var 结束索引 = 开始索引 + 每页数量;

    if (开始索引 >= length) return [];
    if (结束索引 > length) 结束索引 = length;

    return sublist(开始索引, 结束索引);
  }

  Map<String, dynamic> 分页信息(int 页码, int 每页数量) {
    var 总页数 = (length / 每页数量).ceil();
    var 当前页数据 = 获取分页(页码, 每页数量);

    return {
      "当前页": 页码,
      "每页数量": 每页数量,
      "总页数": 总页数,
      "总数量": length,
      "数据": 当前页数据,
    };
  }
}

// 使用
var 产品列表 = List.generate(100, (i) => "产品${i + 1}");
var 第一页 = 产品列表.获取分页(1, 10);  // ["产品1", "产品2", ..., "产品10"]
var 分页信息 = 产品列表.分页信息(1, 10);

// 日期时间扩展
extension 日期时间扩展 on DateTime {
  bool 是今天() {
    var 今天 = DateTime.now();
    return year == 今天.year && month == 今天.month && day == 今天.day;
  }

  String 友好格式() {
    var 现在 = DateTime.now();
    var 差值 = 现在.difference(this);

    if (差值.inMinutes < 1) return "刚刚";
    if (差值.inHours < 1) return "${差值.inMinutes}分钟前";
    if (差值.inDays < 1) return "${差值.inHours}小时前";
    if (差值.inDays < 7) return "${差值.inDays}天前";

    return "${year}-${month.toString().padLeft(2, '0')}-${day.toString().padLeft(2, '0')}";
  }
}

// 使用
var 发布时间 = DateTime.now().subtract(Duration(hours: 3));
print(发布时间.友好格式());  // "3小时前"
```


---

## 第十五章：Flutter中的Dart特性

### 🎨 Widget构造函数的const优化

在Flutter中，正确使用const可以大幅提升性能，减少不必要的Widget重建。

```dart
// ❌ 不好的做法 - 每次build都创建新对象
class BadWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      child: Text('Hello World'),  // 每次build都创建新的Text
    );
  }
}

// ✅ 好的做法 - 使用const constructor
class GoodWidget extends StatelessWidget {
  const GoodWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return const Container(
      child: Text('Hello World'),  // const减少重建
    );
  }
}

// 实际应用：可配置的Widget
class CustomButton extends StatelessWidget {
  const CustomButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.color = Colors.blue,
    this.icon,
  });

  final String text;
  final VoidCallback onPressed;
  final Color color;
  final IconData? icon;

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(backgroundColor: color),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) ...[
            Icon(icon),
            const SizedBox(width: 8),  // const减少内存占用
          ],
          Text(text),
        ],
      ),
    );
  }
}
```

### 🔄 Widget的copyWith模式

Flutter中常用copyWith模式来创建对象的变体，这是Dart中的重要模式。

```dart
// 用户数据模型
class UserProfile {
  final String name;
  final int age;
  final String email;
  final String? avatar;
  final bool isVip;

  const UserProfile({
    required this.name,
    required this.age,
    required this.email,
    this.avatar,
    this.isVip = false,
  });

  // copyWith方法 - Flutter的核心模式
  UserProfile copyWith({
    String? name,
    int? age,
    String? email,
    String? avatar,
    bool? isVip,
  }) {
    return UserProfile(
      name: name ?? this.name,
      age: age ?? this.age,
      email: email ?? this.email,
      avatar: avatar ?? this.avatar,
      isVip: isVip ?? this.isVip,
    );
  }
}

// 使用copyWith更新部分数据
class ProfilePage extends StatefulWidget {
  @override
  _ProfilePageState createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  UserProfile user = const UserProfile(
    name: '张三',
    age: 25,
    email: '<EMAIL>',
  );

  void _updateVipStatus() {
    setState(() {
      // 只更新VIP状态，其他数据保持不变
      user = user.copyWith(isVip: !user.isVip);
    });
  }

  void _updateProfile(String newName, String newEmail) {
    setState(() {
      // 同时更新多个字段
      user = user.copyWith(
        name: newName,
        email: newEmail,
      );
    });
  }
}
```

### 📱 BuildContext的正确使用

BuildContext是Flutter中最重要的概念之一，理解它的生命周期至关重要。

```dart
class ContextExample extends StatefulWidget {
  @override
  _ContextExampleState createState() => _ContextExampleState();
}

class _ContextExampleState extends State<ContextExample> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Context示例')),
      body: Column(
        children: [
          // ❌ 错误的做法 - 可能找不到Scaffold
          ElevatedButton(
            onPressed: () {
              // 这个context可能无法找到Scaffold
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('可能出错')),
              );
            },
            child: const Text('错误的方式'),
          ),

          // ✅ 正确的做法 - 使用Builder确保正确的context
          Builder(
            builder: (BuildContext scaffoldContext) {
              return ElevatedButton(
                onPressed: () {
                  // 使用正确的context
                  ScaffoldMessenger.of(scaffoldContext).showSnackBar(
                    const SnackBar(content: Text('正确显示')),
                  );
                },
                child: const Text('正确的方式'),
              );
            },
          ),

          // 更好的做法 - 使用异步操作
          ElevatedButton(
            onPressed: () => _showSnackBar(context),
            child: const Text('异步方式'),
          ),
        ],
      ),
    );
  }

  // 安全的异步SnackBar显示
  void _showSnackBar(BuildContext context) async {
    // 检查widget是否还在树中
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('异步显示成功')),
    );
  }
}
```


### 🎯 State管理中的Dart特性

#### 1. ValueNotifier和ChangeNotifier

```dart
// 简单的状态管理
class CounterNotifier extends ValueNotifier<int> {
  CounterNotifier() : super(0);

  void increment() {
    value++;  // 自动通知监听者
  }

  void decrement() {
    value--;
  }

  void reset() {
    value = 0;
  }
}

// 复杂状态的管理
class ShoppingCartNotifier extends ChangeNotifier {
  final List<CartItem> _items = [];

  List<CartItem> get items => List.unmodifiable(_items);

  int get itemCount => _items.length;

  double get totalPrice => _items.fold(0, (sum, item) => sum + item.price);

  void addItem(CartItem item) {
    _items.add(item);
    notifyListeners();  // 手动通知监听者
  }

  void removeItem(String itemId) {
    _items.removeWhere((item) => item.id == itemId);
    notifyListeners();
  }

  void clear() {
    _items.clear();
    notifyListeners();
  }
}

// 在Widget中使用
class ShoppingCartWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<int>(
      valueListenable: CounterNotifier(),
      builder: (context, count, child) {
        return Text('计数: $count');
      },
    );
  }
}
```

#### 2. Stream和StreamBuilder

```dart
// 实时数据流
class ChatService {
  final StreamController<ChatMessage> _messageController =
      StreamController<ChatMessage>.broadcast();

  Stream<ChatMessage> get messageStream => _messageController.stream;

  void sendMessage(String content, String userId) {
    final message = ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      userId: userId,
      timestamp: DateTime.now(),
    );

    _messageController.add(message);
  }

  void dispose() {
    _messageController.close();
  }
}

// 在Widget中使用Stream
class ChatWidget extends StatelessWidget {
  final ChatService chatService = ChatService();

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<ChatMessage>(
      stream: chatService.messageStream,
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          return Text('错误: ${snapshot.error}');
        }

        if (!snapshot.hasData) {
          return const Text('暂无消息');
        }

        final message = snapshot.data!;
        return ListTile(
          title: Text(message.content),
          subtitle: Text('来自: ${message.userId}'),
          trailing: Text(message.timestamp.toString()),
        );
      },
    );
  }
}
```

### 🚀 异步操作的Flutter集成

#### 1. FutureBuilder的高级用法

```dart
class UserProfileWidget extends StatelessWidget {
  final String userId;

  const UserProfileWidget({super.key, required this.userId});

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<UserProfile>(
      future: _loadUserProfile(userId),
      builder: (context, snapshot) {
        // 多种状态的处理
        return AnimatedSwitcher(
          duration: const Duration(milliseconds: 300),
          child: _buildContent(snapshot),
        );
      },
    );
  }

  Widget _buildContent(AsyncSnapshot<UserProfile> snapshot) {
    if (snapshot.connectionState == ConnectionState.waiting) {
      return const Center(
        key: ValueKey('loading'),
        child: CircularProgressIndicator(),
      );
    }

    if (snapshot.hasError) {
      return Center(
        key: const ValueKey('error'),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, color: Colors.red, size: 64),
            const SizedBox(height: 16),
            Text('加载失败: ${snapshot.error}'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                // 重新加载
                (context as Element).markNeedsBuild();
              },
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    final user = snapshot.data!;
    return Card(
      key: const ValueKey('success'),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            CircleAvatar(
              radius: 50,
              backgroundImage: user.avatar != null
                ? NetworkImage(user.avatar!)
                : null,
              child: user.avatar == null
                ? Text(user.name[0])
                : null,
            ),
            const SizedBox(height: 16),
            Text(
              user.name,
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            Text(
              user.email,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            if (user.isVip)
              const Chip(
                label: Text('VIP用户'),
                backgroundColor: Colors.gold,
              ),
          ],
        ),
      ),
    );
  }

  Future<UserProfile> _loadUserProfile(String userId) async {
    // 模拟网络延迟
    await Future.delayed(const Duration(seconds: 2));

    // 模拟随机错误
    if (Random().nextDouble() < 0.3) {
      throw Exception('网络连接失败');
    }

    return UserProfile(
      name: '用户$userId',
      age: 25,
      email: 'user$<EMAIL>',
      isVip: Random().nextBool(),
    );
  }
}
```

#### 2. 自定义异步Widget

```dart
// 可复用的异步数据Widget
class AsyncDataWidget<T> extends StatelessWidget {
  const AsyncDataWidget({
    super.key,
    required this.future,
    required this.builder,
    this.loadingBuilder,
    this.errorBuilder,
  });

  final Future<T> future;
  final Widget Function(BuildContext context, T data) builder;
  final Widget Function(BuildContext context)? loadingBuilder;
  final Widget Function(BuildContext context, Object error)? errorBuilder;

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<T>(
      future: future,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return loadingBuilder?.call(context) ??
                 const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return errorBuilder?.call(context, snapshot.error!) ??
                 Center(child: Text('错误: ${snapshot.error}'));
        }

        return builder(context, snapshot.data as T);
      },
    );
  }
}

// 使用自定义异步Widget
class ProductListPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('产品列表')),
      body: AsyncDataWidget<List<Product>>(
        future: ProductService.getProducts(),
        loadingBuilder: (context) => const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('正在加载产品...'),
            ],
          ),
        ),
        errorBuilder: (context, error) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text('加载失败: $error'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('返回'),
              ),
            ],
          ),
        ),
        builder: (context, products) {
          return ListView.builder(
            itemCount: products.length,
            itemBuilder: (context, index) {
              final product = products[index];
              return ListTile(
                title: Text(product.name),
                subtitle: Text('¥${product.price}'),
                trailing: const Icon(Icons.arrow_forward_ios),
              );
            },
          );
        },
      ),
    );
  }
}
```


---

## 第十六章：Widget与State管理

### 🎨 StatefulWidget的生命周期

理解Widget生命周期是Flutter开发的关键，Dart的特性在这里得到充分体现。

```dart
class LifecycleWidget extends StatefulWidget {
  const LifecycleWidget({super.key, required this.title});

  final String title;

  @override
  State<LifecycleWidget> createState() => _LifecycleWidgetState();
}

class _LifecycleWidgetState extends State<LifecycleWidget>
    with WidgetsBindingObserver {

  late String _currentTitle;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    print('1. initState: Widget初始化');

    _currentTitle = widget.title;
    WidgetsBinding.instance.addObserver(this);

    // 启动定时器
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {  // 检查Widget是否还在树中
        setState(() {
          // 更新UI
        });
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print('2. didChangeDependencies: 依赖改变');
    // 在这里可以安全地访问InheritedWidget
  }

  @override
  void didUpdateWidget(LifecycleWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    print('3. didUpdateWidget: Widget更新');

    if (oldWidget.title != widget.title) {
      setState(() {
        _currentTitle = widget.title;
      });
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    print('4. App状态改变: $state');

    switch (state) {
      case AppLifecycleState.resumed:
        print('应用恢复前台');
        break;
      case AppLifecycleState.paused:
        print('应用进入后台');
        break;
      case AppLifecycleState.detached:
        print('应用被销毁');
        break;
      case AppLifecycleState.inactive:
        print('应用非活跃状态');
        break;
      default:
        break;
    }
  }

  @override
  void dispose() {
    print('5. dispose: 清理资源');

    _timer?.cancel();  // 取消定时器
    WidgetsBinding.instance.removeObserver(this);

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    print('6. build: 构建UI');

    return Scaffold(
      appBar: AppBar(title: Text(_currentTitle)),
      body: const Center(
        child: Text('生命周期演示'),
      ),
    );
  }
}
```

### 🔄 高级State管理模式

#### 1. InheritedWidget模式

```dart
// 数据共享的InheritedWidget
class UserDataInherited extends InheritedWidget {
  const UserDataInherited({
    super.key,
    required this.userData,
    required super.child,
  });

  final UserData userData;

  // 提供静态方法方便访问
  static UserDataInherited? of(BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<UserDataInherited>();
  }

  @override
  bool updateShouldNotify(UserDataInherited oldWidget) {
    // 当数据改变时通知依赖的Widget重建
    return userData != oldWidget.userData;
  }
}

// 数据提供者Widget
class UserDataProvider extends StatefulWidget {
  const UserDataProvider({super.key, required this.child});

  final Widget child;

  @override
  State<UserDataProvider> createState() => _UserDataProviderState();
}

class _UserDataProviderState extends State<UserDataProvider> {
  UserData _userData = const UserData(name: '游客', isLoggedIn: false);

  void _updateUser(UserData newUserData) {
    setState(() {
      _userData = newUserData;
    });
  }

  @override
  Widget build(BuildContext context) {
    return UserDataInherited(
      userData: _userData.copyWith(updateCallback: _updateUser),
      child: widget.child,
    );
  }
}

// 消费者Widget
class UserProfile extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final userData = UserDataInherited.of(context)?.userData;

    if (userData == null) {
      return const Text('无法获取用户数据');
    }

    return Card(
      child: Column(
        children: [
          Text('用户: ${userData.name}'),
          Text('状态: ${userData.isLoggedIn ? "已登录" : "未登录"}'),
          ElevatedButton(
            onPressed: () {
              userData.updateCallback?.call(
                userData.copyWith(
                  name: '张三',
                  isLoggedIn: true,
                ),
              );
            },
            child: const Text('模拟登录'),
          ),
        ],
      ),
    );
  }
}
```

#### 2. Provider模式的Dart实现

```dart
// 简化版Provider实现
class SimpleProvider<T> extends InheritedWidget {
  const SimpleProvider({
    super.key,
    required this.data,
    required super.child,
  });

  final T data;

  static T of<T>(BuildContext context) {
    final provider = context.dependOnInheritedWidgetOfExactType<SimpleProvider<T>>();
    assert(provider != null, 'No SimpleProvider<$T> found in context');
    return provider!.data;
  }

  @override
  bool updateShouldNotify(SimpleProvider<T> oldWidget) {
    return data != oldWidget.data;
  }
}

// ChangeNotifierProvider实现
class ChangeNotifierProvider<T extends ChangeNotifier> extends StatefulWidget {
  const ChangeNotifierProvider({
    super.key,
    required this.create,
    required this.child,
  });

  final T Function() create;
  final Widget child;

  @override
  State<ChangeNotifierProvider<T>> createState() =>
      _ChangeNotifierProviderState<T>();
}

class _ChangeNotifierProviderState<T extends ChangeNotifier>
    extends State<ChangeNotifierProvider<T>> {

  late T _notifier;

  @override
  void initState() {
    super.initState();
    _notifier = widget.create();
    _notifier.addListener(_onNotifierChanged);
  }

  void _onNotifierChanged() {
    setState(() {
      // 重建依赖的Widget
    });
  }

  @override
  void dispose() {
    _notifier.removeListener(_onNotifierChanged);
    _notifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SimpleProvider<T>(
      data: _notifier,
      child: widget.child,
    );
  }
}

// 消费者Widget
class Consumer<T> extends StatelessWidget {
  const Consumer({super.key, required this.builder});

  final Widget Function(BuildContext context, T data) builder;

  @override
  Widget build(BuildContext context) {
    final data = SimpleProvider.of<T>(context);
    return builder(context, data);
  }
}
```


---

## 第十七章：平台通道与原生集成

### 📱 MethodChannel的Dart实现

```dart
// 平台通道服务
class PlatformChannelService {
  static const _channel = MethodChannel('com.example.app/platform');

  // 获取设备信息
  static Future<Map<String, dynamic>> getDeviceInfo() async {
    try {
      final result = await _channel.invokeMethod('getDeviceInfo');
      return Map<String, dynamic>.from(result);
    } on PlatformException catch (e) {
      throw Exception('获取设备信息失败: ${e.message}');
    }
  }

  // 调用原生功能
  static Future<bool> openNativeCamera() async {
    try {
      final result = await _channel.invokeMethod('openCamera');
      return result as bool;
    } on PlatformException catch (e) {
      print('相机调用失败: ${e.message}');
      return false;
    }
  }

  // 监听原生事件
  static void listenToNativeEvents() {
    _channel.setMethodCallHandler((call) async {
      switch (call.method) {
        case 'onBatteryChanged':
          final level = call.arguments as int;
          print('电池电量: $level%');
          break;
        case 'onNetworkChanged':
          final isConnected = call.arguments as bool;
          print('网络状态: ${isConnected ? "已连接" : "已断开"}');
          break;
        default:
          print('未知的原生调用: ${call.method}');
      }
    });
  }
}

// 使用平台通道的Widget
class NativeIntegrationWidget extends StatefulWidget {
  @override
  _NativeIntegrationWidgetState createState() =>
      _NativeIntegrationWidgetState();
}

class _NativeIntegrationWidgetState extends State<NativeIntegrationWidget> {
  Map<String, dynamic>? deviceInfo;
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    PlatformChannelService.listenToNativeEvents();
    _loadDeviceInfo();
  }

  Future<void> _loadDeviceInfo() async {
    setState(() => isLoading = true);

    try {
      final info = await PlatformChannelService.getDeviceInfo();
      setState(() {
        deviceInfo = info;
        isLoading = false;
      });
    } catch (e) {
      setState(() => isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('加载失败: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('原生集成')),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ElevatedButton(
              onPressed: () async {
                final success = await PlatformChannelService.openNativeCamera();
                if (!success) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('相机调用失败')),
                  );
                }
              },
              child: const Text('打开原生相机'),
            ),
            const SizedBox(height: 20),
            const Text('设备信息:', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 10),
            if (isLoading)
              const CircularProgressIndicator()
            else if (deviceInfo != null)
              ...deviceInfo!.entries.map((entry) =>
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Row(
                    children: [
                      Text('${entry.key}: ', style: const TextStyle(fontWeight: FontWeight.bold)),
                      Expanded(child: Text('${entry.value}')),
                    ],
                  ),
                ),
              ).toList()
            else
              const Text('无法获取设备信息'),
          ],
        ),
      ),
    );
  }
}
```


---

## 第十八章：代码规范与命名约定

### 📝 官方推荐命名规范

遵循Dart官方命名规范是写出专业代码的第一步。

```dart
// ✅ 好的命名示例

// 类名：大驼峰（PascalCase）
class UserRepository {}
class PaymentService {}
class ShoppingCartItem {}

// 变量和方法名：小驼峰（camelCase）
String userName = 'john';
int userAge = 25;
bool isLoggedIn = false;

void calculateTotalPrice() {}
Future<User> getCurrentUser() async {}

// 常量：小驼峰（不使用全大写）
const maxRetryCount = 3;
const defaultTimeout = Duration(seconds: 30);
const apiBaseUrl = 'https://api.example.com';

// 文件名：下划线分隔（snake_case）
// user_repository.dart
// payment_service.dart
// shopping_cart_item.dart

// 私有成员：以下划线开头
class UserService {
  String _apiKey = 'secret';
  void _validateUser() {}
}

// ❌ 避免的命名
class userRepository {}          // 类名应该大驼峰
const MAX_RETRY_COUNT = 3;       // Dart中常量不用全大写
String user_name = 'john';       // 变量不用下划线
void GetCurrentUser() {}         // 方法名不用大驼峰
```

### 🏗️ 代码组织最佳实践

#### 1. 导入顺序规范

```dart
// 正确的导入顺序
// 1. Dart core libraries
import 'dart:async';
import 'dart:convert';
import 'dart:io';

// 2. Flutter libraries
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// 3. Third-party packages
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

// 4. Local imports
import '../models/user.dart';
import '../services/api_service.dart';
import 'widgets/custom_button.dart';

// 使用别名避免冲突
import 'package:flutter/material.dart' as material;
import 'package:flutter/cupertino.dart' as cupertino;
```

#### 2. 类结构组织

```dart
class WellOrganizedClass {
  // 1. 静态常量
  static const String defaultName = 'Unknown';
  static const int maxAttempts = 3;

  // 2. 静态变量
  static int _instanceCount = 0;

  // 3. 实例常量
  final String id;
  final DateTime createdAt;

  // 4. 实例变量（公共）
  String name;
  int age;

  // 5. 实例变量（私有）
  String _email;
  bool _isActive = true;

  // 6. 构造函数
  WellOrganizedClass({
    required this.id,
    required this.name,
    required this.age,
    String? email,
  }) : createdAt = DateTime.now(),
       _email = email ?? '' {
    _instanceCount++;
  }

  // 7. 命名构造函数
  WellOrganizedClass.guest()
    : id = 'guest_${DateTime.now().millisecondsSinceEpoch}',
      name = 'Guest',
      age = 0,
      createdAt = DateTime.now(),
      _email = '';

  // 8. 工厂构造函数
  factory WellOrganizedClass.fromJson(Map<String, dynamic> json) {
    return WellOrganizedClass(
      id: json['id'],
      name: json['name'],
      age: json['age'],
      email: json['email'],
    );
  }

  // 9. Getters
  String get email => _email;
  bool get isActive => _isActive;
  static int get instanceCount => _instanceCount;

  // 10. Setters
  set email(String value) {
    _email = value.trim().toLowerCase();
  }

  // 11. 公共方法
  void activate() {
    _isActive = true;
  }

  void updateProfile({String? name, int? age}) {
    if (name != null) this.name = name;
    if (age != null) this.age = age;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'age': age,
      'email': _email,
      'isActive': _isActive,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  // 12. 私有方法
  void _validateAge() {
    if (age < 0 || age > 150) {
      throw ArgumentError('Invalid age: $age');
    }
  }

  bool _isValidEmail(String email) {
    return RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(email);
  }

  // 13. 重写方法
  @override
  String toString() {
    return 'WellOrganizedClass(id: $id, name: $name, age: $age)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WellOrganizedClass && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
```

### 🛡️ 错误处理最佳实践

#### 1. 异常类型定义

```dart
// 自定义异常类型
abstract class AppException implements Exception {
  final String message;
  final int? code;
  final dynamic originalError;

  const AppException(this.message, {this.code, this.originalError});

  @override
  String toString() => 'AppException: $message';
}

class NetworkException extends AppException {
  const NetworkException(super.message, {super.code, super.originalError});
}

class ValidationException extends AppException {
  final Map<String, String> fieldErrors;

  const ValidationException(
    super.message,
    this.fieldErrors,
    {super.code, super.originalError}
  );
}

class BusinessLogicException extends AppException {
  const BusinessLogicException(super.message, {super.code, super.originalError});
}
```

#### 2. 统一错误处理

```dart
// Result类型封装
abstract class Result<T> {
  const Result();

  bool get isSuccess => this is Success<T>;
  bool get isFailure => this is Failure<T>;

  T get data => (this as Success<T>).data;
  AppException get error => (this as Failure<T>).error;
}

class Success<T> extends Result<T> {
  final T data;
  const Success(this.data);
}

class Failure<T> extends Result<T> {
  final AppException error;
  const Failure(this.error);
}

// 服务层错误处理
class UserService {
  Future<Result<User>> getUserById(String id) async {
    try {
      // 参数验证
      if (id.isEmpty) {
        return const Failure(ValidationException('用户ID不能为空', {}));
      }

      // 网络请求
      final response = await http.get(
        Uri.parse('$apiBaseUrl/users/$id'),
        headers: {'Authorization': 'Bearer $token'},
      ).timeout(const Duration(seconds: 10));

      // 响应处理
      if (response.statusCode == 200) {
        final userData = jsonDecode(response.body);
        final user = User.fromJson(userData);
        return Success(user);
      } else if (response.statusCode == 404) {
        return const Failure(BusinessLogicException('用户不存在', code: 404));
      } else {
        return Failure(NetworkException(
          'HTTP ${response.statusCode}: ${response.reasonPhrase}',
          code: response.statusCode,
        ));
      }

    } on TimeoutException {
      return const Failure(NetworkException('请求超时，请检查网络连接'));
    } on SocketException {
      return const Failure(NetworkException('网络连接失败'));
    } catch (e, stackTrace) {
      // 记录未预期的错误
      print('Unexpected error in getUserById: $e\n$stackTrace');
      return Failure(AppException('获取用户信息失败', originalError: e));
    }
  }
}

// UI层错误处理
class UserProfileWidget extends StatelessWidget {
  final String userId;

  const UserProfileWidget({super.key, required this.userId});

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Result<User>>(
      future: UserService().getUserById(userId),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const CircularProgressIndicator();
        }

        if (!snapshot.hasData) {
          return const Text('无数据');
        }

        final result = snapshot.data!;

        if (result.isFailure) {
          return _buildErrorWidget(context, result.error);
        }

        return _buildUserWidget(result.data);
      },
    );
  }

  Widget _buildErrorWidget(BuildContext context, AppException error) {
    String userMessage;
    IconData icon;
    Color color;

    switch (error.runtimeType) {
      case NetworkException:
        userMessage = '网络连接问题，请检查网络后重试';
        icon = Icons.wifi_off;
        color = Colors.orange;
        break;
      case ValidationException:
        userMessage = '输入信息有误，请检查后重试';
        icon = Icons.error_outline;
        color = Colors.red;
        break;
      case BusinessLogicException:
        userMessage = error.message;
        icon = Icons.info_outline;
        color = Colors.blue;
        break;
      default:
        userMessage = '操作失败，请稍后重试';
        icon = Icons.error;
        color = Colors.red;
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 64, color: color),
            const SizedBox(height: 16),
            Text(
              userMessage,
              textAlign: TextAlign.center,
              style: TextStyle(color: color),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                // 重试逻辑
                (context as Element).markNeedsBuild();
              },
              child: const Text('重试'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserWidget(User user) {
    return Card(
      child: ListTile(
        leading: CircleAvatar(child: Text(user.name[0])),
        title: Text(user.name),
        subtitle: Text(user.email),
      ),
    );
  }
}
```

### 📦 项目架构与代码组织

#### 1. 文件夹结构规范

```
lib/
├── core/                          # 核心基础设施
│   ├── constants/                 # 常量定义
│   │   ├── app_constants.dart
│   │   └── api_constants.dart
│   ├── errors/                    # 错误处理
│   │   ├── exceptions.dart
│   │   └── failures.dart
│   ├── network/                   # 网络层
│   │   ├── dio_client.dart
│   │   └── network_info.dart
│   ├── utils/                     # 工具类
│   │   ├── validators.dart
│   │   ├── formatters.dart
│   │   └── extensions.dart
│   └── usecases/                  # 用例基类
│       └── usecase.dart
├── features/                      # 功能模块
│   ├── auth/                      # 认证模块
│   │   ├── data/
│   │   │   ├── datasources/
│   │   │   │   ├── auth_local_data_source.dart
│   │   │   │   └── auth_remote_data_source.dart
│   │   │   ├── models/
│   │   │   │   └── user_model.dart
│   │   │   └── repositories/
│   │   │       └── auth_repository_impl.dart
│   │   ├── domain/
│   │   │   ├── entities/
│   │   │   │   └── user.dart
│   │   │   ├── repositories/
│   │   │   │   └── auth_repository.dart
│   │   │   └── usecases/
│   │   │       ├── login_usecase.dart
│   │   │       └── logout_usecase.dart
│   │   └── presentation/
│   │       ├── bloc/
│   │       │   ├── auth_bloc.dart
│   │       │   ├── auth_event.dart
│   │       │   └── auth_state.dart
│   │       ├── pages/
│   │       │   ├── login_page.dart
│   │       │   └── profile_page.dart
│   │       └── widgets/
│   │           ├── login_form.dart
│   │           └── profile_card.dart
│   └── products/                  # 产品模块
│       └── ...                    # 相同结构
├── shared/                        # 共享组件
│   ├── widgets/                   # 通用UI组件
│   │   ├── loading_widget.dart
│   │   ├── error_widget.dart
│   │   └── empty_state_widget.dart
│   ├── themes/                    # 主题定义
│   │   └── app_theme.dart
│   └── l10n/                      # 国际化
│       └── app_localizations.dart
└── main.dart                      # 应用入口
```

#### 2. 模块化开发规范

```dart
// feature/auth/domain/repositories/auth_repository.dart
abstract class AuthRepository {
  Future<Result<User>> login(String email, String password);
  Future<Result<void>> logout();
  Future<Result<User?>> getCurrentUser();
}

// feature/auth/data/repositories/auth_repository_impl.dart
class AuthRepositoryImpl implements AuthRepository {
  final AuthRemoteDataSource remoteDataSource;
  final AuthLocalDataSource localDataSource;
  final NetworkInfo networkInfo;

  const AuthRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
  });

  @override
  Future<Result<User>> login(String email, String password) async {
    if (await networkInfo.isConnected) {
      try {
        final userModel = await remoteDataSource.login(email, password);
        await localDataSource.cacheUser(userModel);
        return Success(userModel.toEntity());
      } catch (e) {
        return Failure(_handleException(e));
      }
    } else {
      return const Failure(NetworkException('无网络连接'));
    }
  }

  AppException _handleException(dynamic error) {
    if (error is DioException) {
      switch (error.response?.statusCode) {
        case 401:
          return const BusinessLogicException('用户名或密码错误');
        case 404:
          return const BusinessLogicException('用户不存在');
        default:
          return NetworkException('网络请求失败: ${error.message}');
      }
    }
    return AppException('未知错误', originalError: error);
  }
}

// feature/auth/domain/usecases/login_usecase.dart
class LoginUseCase {
  final AuthRepository repository;

  const LoginUseCase(this.repository);

  Future<Result<User>> call(LoginParams params) async {
    // 参数验证
    final validation = _validateParams(params);
    if (validation != null) {
      return Failure(validation);
    }

    return await repository.login(params.email, params.password);
  }

  ValidationException? _validateParams(LoginParams params) {
    final errors = <String, String>{};

    if (params.email.isEmpty) {
      errors['email'] = '邮箱不能为空';
    } else if (!_isValidEmail(params.email)) {
      errors['email'] = '邮箱格式不正确';
    }

    if (params.password.isEmpty) {
      errors['password'] = '密码不能为空';
    } else if (params.password.length < 6) {
      errors['password'] = '密码至少6位';
    }

    return errors.isEmpty ? null : ValidationException('参数验证失败', errors);
  }

  bool _isValidEmail(String email) {
    return RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(email);
  }
}

class LoginParams {
  final String email;
  final String password;

  const LoginParams({required this.email, required this.password});
}
```

### 🧪 测试驱动开发

#### 1. 单元测试规范

```dart
// test/features/auth/domain/usecases/login_usecase_test.dart
void main() {
  group('LoginUseCase', () {
    late LoginUseCase useCase;
    late MockAuthRepository mockRepository;

    setUp(() {
      mockRepository = MockAuthRepository();
      useCase = LoginUseCase(mockRepository);
    });

    group('参数验证', () {
      test('应该返回邮箱为空的验证错误', () async {
        // Arrange
        const params = LoginParams(email: '', password: 'password123');

        // Act
        final result = await useCase(params);

        // Assert
        expect(result.isFailure, true);
        expect(result.error, isA<ValidationException>());
        final error = result.error as ValidationException;
        expect(error.fieldErrors['email'], '邮箱不能为空');
      });

      test('应该返回邮箱格式错误的验证错误', () async {
        // Arrange
        const params = LoginParams(email: 'invalid-email', password: 'password123');

        // Act
        final result = await useCase(params);

        // Assert
        expect(result.isFailure, true);
        expect(result.error, isA<ValidationException>());
        final error = result.error as ValidationException;
        expect(error.fieldErrors['email'], '邮箱格式不正确');
      });
    });

    group('业务逻辑', () {
      test('应该成功登录并返回用户信息', () async {
        // Arrange
        const params = LoginParams(
          email: '<EMAIL>',
          password: 'password123'
        );
        const user = User(id: '1', name: 'Test User', email: '<EMAIL>');

        when(() => mockRepository.login(any(), any()))
            .thenAnswer((_) async => const Success(user));

        // Act
        final result = await useCase(params);

        // Assert
        expect(result.isSuccess, true);
        expect(result.data, user);
        verify(() => mockRepository.login('<EMAIL>', 'password123'));
      });

      test('应该处理网络错误', () async {
        // Arrange
        const params = LoginParams(
          email: '<EMAIL>',
          password: 'password123'
        );

        when(() => mockRepository.login(any(), any()))
            .thenAnswer((_) async => const Failure(NetworkException('网络连接失败')));

        // Act
        final result = await useCase(params);

        // Assert
        expect(result.isFailure, true);
        expect(result.error, isA<NetworkException>());
      });
    });
  });
}
```

#### 2. Widget测试规范

```dart
// test/features/auth/presentation/pages/login_page_test.dart
void main() {
  group('LoginPage', () {
    testWidgets('应该显示登录表单', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider(
            create: (_) => MockAuthBloc(),
            child: const LoginPage(),
          ),
        ),
      );

      // Assert
      expect(find.byType(TextFormField), findsNWidgets(2)); // 邮箱和密码输入框
      expect(find.byType(ElevatedButton), findsOneWidget); // 登录按钮
      expect(find.text('登录'), findsOneWidget);
    });

    testWidgets('应该在输入无效邮箱时显示错误', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider(
            create: (_) => MockAuthBloc(),
            child: const LoginPage(),
          ),
        ),
      );

      // Act
      await tester.enterText(find.byKey(const Key('emailField')), 'invalid-email');
      await tester.enterText(find.byKey(const Key('passwordField')), 'password123');
      await tester.tap(find.byType(ElevatedButton));
      await tester.pump();

      // Assert
      expect(find.text('邮箱格式不正确'), findsOneWidget);
    });
  });
}
```


---

## 第十九章：项目架构与代码组织

### 🏗️ Clean Architecture在Dart中的实现

Clean Architecture是一种分层架构，在Dart/Flutter项目中有标准的实现模式。

#### 1. 架构层次划分

```dart
// 领域层 (Domain Layer) - 业务逻辑核心
abstract class UserRepository {
  Future<Result<User>> getUser(String id);
  Future<Result<void>> saveUser(User user);
}

class GetUserUseCase {
  final UserRepository repository;

  const GetUserUseCase(this.repository);

  Future<Result<User>> call(String userId) async {
    if (userId.isEmpty) {
      return const Failure(ValidationException('用户ID不能为空', {}));
    }

    return await repository.getUser(userId);
  }
}

// 数据层 (Data Layer) - 数据访问实现
class UserRepositoryImpl implements UserRepository {
  final UserRemoteDataSource remoteDataSource;
  final UserLocalDataSource localDataSource;
  final NetworkInfo networkInfo;

  const UserRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
  });

  @override
  Future<Result<User>> getUser(String id) async {
    if (await networkInfo.isConnected) {
      try {
        final userModel = await remoteDataSource.getUser(id);
        await localDataSource.cacheUser(userModel);
        return Success(userModel.toEntity());
      } catch (e) {
        // 网络失败时尝试从缓存获取
        try {
          final cachedUser = await localDataSource.getCachedUser(id);
          return Success(cachedUser.toEntity());
        } catch (cacheError) {
          return Failure(_mapException(e));
        }
      }
    } else {
      // 离线模式
      try {
        final cachedUser = await localDataSource.getCachedUser(id);
        return Success(cachedUser.toEntity());
      } catch (e) {
        return const Failure(NetworkException('无网络连接且无缓存数据'));
      }
    }
  }
}

// 表现层 (Presentation Layer) - UI逻辑
class UserBloc extends Bloc<UserEvent, UserState> {
  final GetUserUseCase getUserUseCase;

  UserBloc({required this.getUserUseCase}) : super(const UserState()) {
    on<UserRequested>(_onUserRequested);
  }

  Future<void> _onUserRequested(
    UserRequested event,
    Emitter<UserState> emit,
  ) async {
    emit(state.copyWith(status: UserStatus.loading));

    final result = await getUserUseCase(event.userId);

    result.fold(
      (failure) => emit(state.copyWith(
        status: UserStatus.failure,
        error: failure.message,
      )),
      (user) => emit(state.copyWith(
        status: UserStatus.success,
        user: user,
      )),
    );
  }
}
```

#### 2. 依赖注入架构

```dart
// 服务定位器模式
class ServiceLocator {
  static final GetIt _instance = GetIt.instance;

  static void setupDependencies() {
    // 核心服务
    _instance.registerLazySingleton<NetworkInfo>(
      () => NetworkInfoImpl(),
    );

    _instance.registerLazySingleton<DioClient>(
      () => DioClient(),
    );

    // 数据源
    _instance.registerLazySingleton<UserRemoteDataSource>(
      () => UserRemoteDataSourceImpl(_instance<DioClient>()),
    );

    _instance.registerLazySingleton<UserLocalDataSource>(
      () => UserLocalDataSourceImpl(),
    );

    // 仓库
    _instance.registerLazySingleton<UserRepository>(
      () => UserRepositoryImpl(
        remoteDataSource: _instance<UserRemoteDataSource>(),
        localDataSource: _instance<UserLocalDataSource>(),
        networkInfo: _instance<NetworkInfo>(),
      ),
    );

    // 用例
    _instance.registerLazySingleton<GetUserUseCase>(
      () => GetUserUseCase(_instance<UserRepository>()),
    );

    // BLoC
    _instance.registerFactory<UserBloc>(
      () => UserBloc(getUserUseCase: _instance<GetUserUseCase>()),
    );
  }

  static T get<T extends Object>() => _instance<T>();

  static void reset() => _instance.reset();
}

// 使用Injectable的自动化版本
@module
abstract class AppModule {
  @lazySingleton
  NetworkInfo get networkInfo => NetworkInfoImpl();

  @lazySingleton
  DioClient get dioClient => DioClient();

  @LazySingleton(as: UserRepository)
  UserRepositoryImpl getUserRepository(
    UserRemoteDataSource remoteDataSource,
    UserLocalDataSource localDataSource,
    NetworkInfo networkInfo,
  ) => UserRepositoryImpl(
    remoteDataSource: remoteDataSource,
    localDataSource: localDataSource,
    networkInfo: networkInfo,
  );
}
```

### 📁 模块化项目结构

#### 1. 按功能模块组织

```
lib/
├── core/                          # 核心基础设施
│   ├── di/                       # 依赖注入
│   │   ├── injection.dart
│   │   └── injection.config.dart
│   ├── error/                    # 错误处理
│   │   ├── exceptions.dart
│   │   ├── failures.dart
│   │   └── error_handler.dart
│   ├── network/                  # 网络层
│   │   ├── api_client.dart
│   │   ├── network_info.dart
│   │   └── interceptors/
│   ├── storage/                  # 存储层
│   │   ├── local_storage.dart
│   │   └── secure_storage.dart
│   ├── utils/                    # 工具类
│   │   ├── constants.dart
│   │   ├── extensions.dart
│   │   ├── validators.dart
│   │   └── formatters.dart
│   └── usecases/                 # 用例基类
│       └── usecase.dart
├── features/                     # 功能模块
│   ├── authentication/           # 认证模块
│   │   ├── data/
│   │   │   ├── datasources/
│   │   │   │   ├── auth_local_datasource.dart
│   │   │   │   └── auth_remote_datasource.dart
│   │   │   ├── models/
│   │   │   │   ├── user_model.dart
│   │   │   │   └── token_model.dart
│   │   │   └── repositories/
│   │   │       └── auth_repository_impl.dart
│   │   ├── domain/
│   │   │   ├── entities/
│   │   │   │   ├── user.dart
│   │   │   │   └── auth_token.dart
│   │   │   ├── repositories/
│   │   │   │   └── auth_repository.dart
│   │   │   └── usecases/
│   │   │       ├── login_usecase.dart
│   │   │       ├── logout_usecase.dart
│   │   │       ├── register_usecase.dart
│   │   │       └── refresh_token_usecase.dart
│   │   └── presentation/
│   │       ├── bloc/
│   │       │   ├── auth_bloc.dart
│   │       │   ├── auth_event.dart
│   │       │   └── auth_state.dart
│   │       ├── pages/
│   │       │   ├── login_page.dart
│   │       │   ├── register_page.dart
│   │       │   └── forgot_password_page.dart
│   │       └── widgets/
│   │           ├── login_form.dart
│   │           ├── social_login_buttons.dart
│   │           └── auth_text_field.dart
│   ├── profile/                  # 用户资料模块
│   │   └── ...                   # 相同结构
│   └── settings/                 # 设置模块
│       └── ...                   # 相同结构
├── shared/                       # 共享组件
│   ├── widgets/                  # 通用UI组件
│   │   ├── buttons/
│   │   │   ├── primary_button.dart
│   │   │   └── secondary_button.dart
│   │   ├── inputs/
│   │   │   ├── custom_text_field.dart
│   │   │   └── search_field.dart
│   │   ├── dialogs/
│   │   │   ├── confirm_dialog.dart
│   │   │   └── loading_dialog.dart
│   │   └── layouts/
│   │       ├── scaffold_with_nav.dart
│   │       └── centered_layout.dart
│   ├── themes/                   # 主题系统
│   │   ├── app_theme.dart
│   │   ├── app_colors.dart
│   │   ├── app_text_styles.dart
│   │   └── app_dimensions.dart
│   ├── l10n/                     # 国际化
│   │   ├── app_localizations.dart
│   │   ├── app_en.arb
│   │   └── app_zh.arb
│   └── extensions/               # 扩展方法
│       ├── context_extensions.dart
│       ├── string_extensions.dart
│       └── date_extensions.dart
└── app.dart                      # 应用根组件
```

#### 2. 模块间通信规范

```dart
// 事件总线模式
class AppEventBus {
  static final _instance = StreamController<AppEvent>.broadcast();

  static Stream<T> on<T extends AppEvent>() {
    return _instance.stream.where((event) => event is T).cast<T>();
  }

  static void fire(AppEvent event) {
    _instance.add(event);
  }

  static void dispose() {
    _instance.close();
  }
}

// 应用事件定义
abstract class AppEvent {
  const AppEvent();
}

class UserLoggedInEvent extends AppEvent {
  final User user;
  const UserLoggedInEvent(this.user);
}

class UserLoggedOutEvent extends AppEvent {
  const UserLoggedOutEvent();
}

// 跨模块通信示例
class ProfileBloc extends Bloc<ProfileEvent, ProfileState> {
  StreamSubscription<UserLoggedOutEvent>? _logoutSubscription;

  ProfileBloc() : super(const ProfileState()) {
    // 监听用户登出事件
    _logoutSubscription = AppEventBus.on<UserLoggedOutEvent>().listen(
      (_) => add(const ProfileResetEvent()),
    );
  }

  @override
  Future<void> close() {
    _logoutSubscription?.cancel();
    return super.close();
  }
}
```

### 🔧 配置管理最佳实践

#### 1. 环境配置管理

```dart
// 配置基类
abstract class AppConfig {
  String get apiBaseUrl;
  String get appName;
  bool get isDebugMode;
  Duration get requestTimeout;
}

// 开发环境配置
class DevelopmentConfig implements AppConfig {
  @override
  String get apiBaseUrl => 'https://dev-api.example.com';

  @override
  String get appName => 'MyApp (Dev)';

  @override
  bool get isDebugMode => true;

  @override
  Duration get requestTimeout => const Duration(seconds: 30);
}

// 生产环境配置
class ProductionConfig implements AppConfig {
  @override
  String get apiBaseUrl => 'https://api.example.com';

  @override
  String get appName => 'MyApp';

  @override
  bool get isDebugMode => false;

  @override
  Duration get requestTimeout => const Duration(seconds: 10);
}

// 配置工厂
class ConfigFactory {
  static AppConfig createConfig() {
    const environment = String.fromEnvironment('ENV', defaultValue: 'dev');

    switch (environment) {
      case 'prod':
        return ProductionConfig();
      case 'staging':
        return StagingConfig();
      default:
        return DevelopmentConfig();
    }
  }
}

// 全局配置访问
class Config {
  static late final AppConfig _config;

  static void initialize() {
    _config = ConfigFactory.createConfig();
  }

  static AppConfig get current => _config;
}
```

#### 2. 特性开关管理

```dart
// 特性开关服务
class FeatureFlags {
  static const _flags = <String, bool>{
    'new_ui_design': true,
    'dark_mode': true,
    'push_notifications': false,
    'analytics': true,
    'chat_feature': false,
  };

  static bool isEnabled(String feature) {
    return _flags[feature] ?? false;
  }

  // 远程配置支持
  static Future<void> loadRemoteFlags() async {
    try {
      final response = await http.get(Uri.parse('${Config.current.apiBaseUrl}/flags'));
      if (response.statusCode == 200) {
        final remoteFlags = jsonDecode(response.body) as Map<String, dynamic>;
        _flags.addAll(remoteFlags.cast<String, bool>());
      }
    } catch (e) {
      print('Failed to load remote feature flags: $e');
    }
  }
}

// 使用示例
class HomeWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          const Text('Welcome to MyApp'),

          if (FeatureFlags.isEnabled('new_ui_design'))
            const NewDesignWidget()
          else
            const OldDesignWidget(),

          if (FeatureFlags.isEnabled('chat_feature'))
            FloatingActionButton(
              onPressed: () => Navigator.pushNamed(context, '/chat'),
              child: const Icon(Icons.chat),
            ),
        ],
      ),
    );
  }
}
```


---

## 第二十章：错误处理与异常管理

### 🚨 全局错误处理策略

#### 1. 异常分层处理

```dart
// 异常层次结构
abstract class AppException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;
  final StackTrace? stackTrace;

  const AppException(
    this.message, {
    this.code,
    this.originalError,
    this.stackTrace,
  });

  // 用户友好的错误信息
  String get userMessage;

  // 是否需要重试
  bool get isRetryable => false;

  // 错误级别
  ErrorLevel get level => ErrorLevel.error;
}

enum ErrorLevel { info, warning, error, critical }

// 网络相关异常
class NetworkException extends AppException {
  const NetworkException(
    super.message, {
    super.code,
    super.originalError,
    super.stackTrace,
  });

  @override
  String get userMessage {
    switch (code) {
      case 'timeout':
        return '请求超时，请检查网络连接';
      case 'no_internet':
        return '无网络连接，请检查网络设置';
      case 'server_error':
        return '服务器暂时无法响应，请稍后重试';
      default:
        return '网络连接异常，请重试';
    }
  }

  @override
  bool get isRetryable => true;
}

// 业务逻辑异常
class BusinessException extends AppException {
  const BusinessException(
    super.message, {
    super.code,
    super.originalError,
    super.stackTrace,
  });

  @override
  String get userMessage => message;

  @override
  ErrorLevel get level {
    switch (code) {
      case 'unauthorized':
      case 'forbidden':
        return ErrorLevel.warning;
      default:
        return ErrorLevel.error;
    }
  }
}

// 验证异常
class ValidationException extends AppException {
  final Map<String, List<String>> fieldErrors;

  const ValidationException(
    super.message,
    this.fieldErrors, {
    super.code,
    super.originalError,
    super.stackTrace,
  });

  @override
  String get userMessage => '输入信息有误，请检查后重试';

  String? getFieldError(String field) {
    final errors = fieldErrors[field];
    return errors?.isNotEmpty == true ? errors!.first : null;
  }
}
```

#### 2. 全局错误处理器

```dart
// 全局错误处理服务
class GlobalErrorHandler {
  static final _instance = GlobalErrorHandler._internal();
  factory GlobalErrorHandler() => _instance;
  GlobalErrorHandler._internal();

  final StreamController<AppException> _errorController =
      StreamController<AppException>.broadcast();

  Stream<AppException> get errorStream => _errorController.stream;

  // 处理Flutter框架错误
  static void initialize() {
    FlutterError.onError = (FlutterErrorDetails details) {
      _instance._handleFlutterError(details);
    };

    PlatformDispatcher.instance.onError = (error, stack) {
      _instance._handlePlatformError(error, stack);
      return true;
    };
  }

  void _handleFlutterError(FlutterErrorDetails details) {
    final exception = AppException(
      details.summary.toString(),
      originalError: details.exception,
      stackTrace: details.stack,
    );

    _reportError(exception);

    if (Config.current.isDebugMode) {
      FlutterError.presentError(details);
    }
  }

  void _handlePlatformError(Object error, StackTrace stack) {
    final exception = AppException(
      error.toString(),
      originalError: error,
      stackTrace: stack,
    );

    _reportError(exception);
  }

  // 手动报告错误
  void reportError(AppException exception) {
    _reportError(exception);
  }

  void _reportError(AppException exception) {
    // 添加到错误流
    _errorController.add(exception);

    // 上报到崩溃分析服务
    _sendToCrashlytics(exception);

    // 本地日志
    _logError(exception);

    // 发送到分析服务
    _sendToAnalytics(exception);
  }

  void _sendToCrashlytics(AppException exception) {
    if (!Config.current.isDebugMode) {
      FirebaseCrashlytics.instance.recordError(
        exception.originalError ?? exception,
        exception.stackTrace,
        reason: exception.message,
        information: [
          'Error Code: ${exception.code}',
          'Level: ${exception.level}',
        ],
      );
    }
  }

  void _logError(AppException exception) {
    final log = '''
[${DateTime.now().toIso8601String()}] ${exception.level.name.toUpperCase()}
Message: ${exception.message}
Code: ${exception.code}
Original: ${exception.originalError}
Stack: ${exception.stackTrace}
''';

    print(log);

    // 写入本地日志文件
    LocalLogger.writeError(log);
  }

  void _sendToAnalytics(AppException exception) {
    AnalyticsService.trackError({
      'error_type': exception.runtimeType.toString(),
      'error_message': exception.message,
      'error_code': exception.code,
      'error_level': exception.level.name,
    });
  }
}

// 错误边界Widget
class ErrorBoundary extends StatefulWidget {
  final Widget child;
  final Widget Function(BuildContext context, AppException error)? errorBuilder;

  const ErrorBoundary({
    super.key,
    required this.child,
    this.errorBuilder,
  });

  @override
  State<ErrorBoundary> createState() => _ErrorBoundaryState();
}

class _ErrorBoundaryState extends State<ErrorBoundary> {
  AppException? _error;

  @override
  Widget build(BuildContext context) {
    if (_error != null) {
      return widget.errorBuilder?.call(context, _error!) ??
             _buildDefaultErrorWidget();
    }

    return widget.child;
  }

  Widget _buildDefaultErrorWidget() {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              _error!.userMessage,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 24),
            if (_error!.isRetryable) ...[
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _error = null;
                  });
                },
                child: const Text('重试'),
              ),
            ],
            TextButton(
              onPressed: () {
                Navigator.of(context).pushReplacementNamed('/');
              },
              child: const Text('返回首页'),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void didChangeWidget(ErrorBoundary oldWidget) {
    super.didChangeWidget(oldWidget);
    if (widget.child != oldWidget.child) {
      _error = null;
    }
  }
}
```

### 📊 错误监控与分析

#### 1. 错误统计服务

```dart
class ErrorAnalytics {
  static final Map<String, int> _errorCounts = {};
  static final Map<String, DateTime> _firstOccurrence = {};
  static final Map<String, DateTime> _lastOccurrence = {};

  static void trackError(AppException exception) {
    final key = '${exception.runtimeType}_${exception.code}';

    _errorCounts[key] = (_errorCounts[key] ?? 0) + 1;
    _lastOccurrence[key] = DateTime.now();
    _firstOccurrence[key] ??= DateTime.now();

    // 发送到远程分析服务
    _sendToRemoteAnalytics(exception, _errorCounts[key]!);
  }

  static Map<String, dynamic> getErrorSummary() {
    return {
      'total_errors': _errorCounts.values.fold(0, (sum, count) => sum + count),
      'unique_errors': _errorCounts.length,
      'error_details': _errorCounts.entries.map((entry) => {
        'type': entry.key,
        'count': entry.value,
        'first_seen': _firstOccurrence[entry.key]?.toIso8601String(),
        'last_seen': _lastOccurrence[entry.key]?.toIso8601String(),
      }).toList(),
    };
  }

  static void _sendToRemoteAnalytics(AppException exception, int count) {
    // 实现远程分析服务集成
    AnalyticsService.track('error_occurred', {
      'error_type': exception.runtimeType.toString(),
      'error_code': exception.code,
      'error_level': exception.level.name,
      'occurrence_count': count,
      'is_retryable': exception.isRetryable,
    });
  }
}
```


---

## 第二十一章：性能优化技巧

### ⚡ 内存管理优化

#### 1. 对象生命周期管理

```dart
// 资源管理基类
abstract class Disposable {
  bool _disposed = false;

  bool get isDisposed => _disposed;

  void dispose() {
    if (_disposed) return;
    _disposed = true;
    onDispose();
  }

  void onDispose();

  void checkNotDisposed() {
    if (_disposed) {
      throw StateError('Object has been disposed');
    }
  }
}

// 服务基类with自动资源管理
abstract class BaseService extends Disposable {
  final List<StreamSubscription> _subscriptions = [];
  final List<Timer> _timers = [];

  void addSubscription(StreamSubscription subscription) {
    checkNotDisposed();
    _subscriptions.add(subscription);
  }

  void addTimer(Timer timer) {
    checkNotDisposed();
    _timers.add(timer);
  }

  @override
  void onDispose() {
    // 取消所有订阅
    for (final subscription in _subscriptions) {
      subscription.cancel();
    }
    _subscriptions.clear();

    // 取消所有定时器
    for (final timer in _timers) {
      timer.cancel();
    }
    _timers.clear();
  }
}

// 实际应用示例
class ChatService extends BaseService {
  final StreamController<ChatMessage> _messageController =
      StreamController<ChatMessage>.broadcast();

  Stream<ChatMessage> get messageStream => _messageController.stream;

  ChatService() {
    _initialize();
  }

  void _initialize() {
    // WebSocket连接
    final wsSubscription = _connectToWebSocket().listen(
      (message) => _messageController.add(message),
      onError: (error) => GlobalErrorHandler().reportError(
        NetworkException('WebSocket错误: $error'),
      ),
    );
    addSubscription(wsSubscription);

    // 定期心跳
    final heartbeatTimer = Timer.periodic(
      const Duration(seconds: 30),
      (_) => _sendHeartbeat(),
    );
    addTimer(heartbeatTimer);
  }

  Stream<ChatMessage> _connectToWebSocket() {
    // WebSocket实现
    return Stream.empty();
  }

  void _sendHeartbeat() {
    checkNotDisposed();
    // 发送心跳包
  }

  @override
  void onDispose() {
    _messageController.close();
    super.onDispose();
  }
}
```

#### 2. 缓存策略优化

```dart
// LRU缓存实现
class LRUCache<K, V> {
  final int _maxSize;
  final Map<K, _CacheNode<K, V>> _cache = {};
  _CacheNode<K, V>? _head;
  _CacheNode<K, V>? _tail;

  LRUCache(this._maxSize);

  V? get(K key) {
    final node = _cache[key];
    if (node == null) return null;

    // 移到头部（最近使用）
    _moveToHead(node);
    return node.value;
  }

  void put(K key, V value) {
    final existingNode = _cache[key];

    if (existingNode != null) {
      existingNode.value = value;
      _moveToHead(existingNode);
      return;
    }

    final newNode = _CacheNode(key, value);
    _cache[key] = newNode;
    _addToHead(newNode);

    if (_cache.length > _maxSize) {
      final tail = _removeTail();
      if (tail != null) {
        _cache.remove(tail.key);
      }
    }
  }

  void _addToHead(_CacheNode<K, V> node) {
    node.prev = null;
    node.next = _head;

    if (_head != null) {
      _head!.prev = node;
    }
    _head = node;

    if (_tail == null) {
      _tail = _head;
    }
  }

  void _moveToHead(_CacheNode<K, V> node) {
    _removeNode(node);
    _addToHead(node);
  }

  void _removeNode(_CacheNode<K, V> node) {
    if (node.prev != null) {
      node.prev!.next = node.next;
    } else {
      _head = node.next;
    }

    if (node.next != null) {
      node.next!.prev = node.prev;
    } else {
      _tail = node.prev;
    }
  }

  _CacheNode<K, V>? _removeTail() {
    final lastNode = _tail;
    if (lastNode != null) {
      _removeNode(lastNode);
    }
    return lastNode;
  }

  int get length => _cache.length;
  bool get isEmpty => _cache.isEmpty;
  bool get isNotEmpty => _cache.isNotEmpty;

  void clear() {
    _cache.clear();
    _head = null;
    _tail = null;
  }
}

class _CacheNode<K, V> {
  K key;
  V value;
  _CacheNode<K, V>? prev;
  _CacheNode<K, V>? next;

  _CacheNode(this.key, this.value);
}

// 智能图片缓存
class SmartImageCache {
  static final LRUCache<String, Uint8List> _memoryCache =
      LRUCache<String, Uint8List>(50);
  static const String _diskCacheDir = 'image_cache';

  static Future<Uint8List?> getImage(String url) async {
    // 1. 尝试内存缓存
    final memoryResult = _memoryCache.get(url);
    if (memoryResult != null) return memoryResult;

    // 2. 尝试磁盘缓存
    final diskResult = await _getDiskCachedImage(url);
    if (diskResult != null) {
      _memoryCache.put(url, diskResult);
      return diskResult;
    }

    // 3. 网络请求
    final networkResult = await _fetchFromNetwork(url);
    if (networkResult != null) {
      _memoryCache.put(url, networkResult);
      _saveToDiskCache(url, networkResult);
      return networkResult;
    }

    return null;
  }

  static Future<Uint8List?> _getDiskCachedImage(String url) async {
    try {
      final file = await _getCacheFile(url);
      if (await file.exists()) {
        return await file.readAsBytes();
      }
    } catch (e) {
      print('磁盘缓存读取失败: $e');
    }
    return null;
  }

  static Future<Uint8List?> _fetchFromNetwork(String url) async {
    try {
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        return response.bodyBytes;
      }
    } catch (e) {
      print('网络请求失败: $e');
    }
    return null;
  }

  static void _saveToDiskCache(String url, Uint8List data) async {
    try {
      final file = await _getCacheFile(url);
      await file.writeAsBytes(data);
    } catch (e) {
      print('磁盘缓存保存失败: $e');
    }
  }

  static Future<File> _getCacheFile(String url) async {
    final directory = await getTemporaryDirectory();
    final cacheDir = Directory('${directory.path}/$_diskCacheDir');
    if (!await cacheDir.exists()) {
      await cacheDir.create(recursive: true);
    }

    final fileName = url.hashCode.toString();
    return File('${cacheDir.path}/$fileName');
  }

  static void clearCache() {
    _memoryCache.clear();
    _clearDiskCache();
  }

  static void _clearDiskCache() async {
    try {
      final directory = await getTemporaryDirectory();
      final cacheDir = Directory('${directory.path}/$_diskCacheDir');
      if (await cacheDir.exists()) {
        await cacheDir.delete(recursive: true);
      }
    } catch (e) {
      print('磁盘缓存清理失败: $e');
    }
  }
}
```

### 🚀 渲染性能优化

#### 1. Widget重建优化

```dart
// 性能监控Widget
class PerformanceMonitor extends StatefulWidget {
  final Widget child;
  final String name;

  const PerformanceMonitor({
    super.key,
    required this.child,
    required this.name,
  });

  @override
  State<PerformanceMonitor> createState() => _PerformanceMonitorState();
}

class _PerformanceMonitorState extends State<PerformanceMonitor> {
  int _buildCount = 0;
  DateTime? _lastBuildTime;

  @override
  Widget build(BuildContext context) {
    final now = DateTime.now();
    if (_lastBuildTime != null) {
      final timeSinceLastBuild = now.difference(_lastBuildTime!);
      if (timeSinceLastBuild.inMilliseconds < 16) { // 小于一帧时间
        print('WARNING: ${widget.name} 重建过于频繁 - ${timeSinceLastBuild.inMilliseconds}ms');
      }
    }

    _buildCount++;
    _lastBuildTime = now;

    if (kDebugMode && _buildCount > 10) {
      print('INFO: ${widget.name} 已重建 $_buildCount 次');
    }

    return widget.child;
  }
}

// 优化的列表Widget
class OptimizedListView<T> extends StatefulWidget {
  final List<T> items;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final Widget Function()? emptyBuilder;
  final int? cacheExtent;

  const OptimizedListView({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.emptyBuilder,
    this.cacheExtent,
  });

  @override
  State<OptimizedListView<T>> createState() => _OptimizedListViewState<T>();
}

class _OptimizedListViewState<T> extends State<OptimizedListView<T>> {
  @override
  Widget build(BuildContext context) {
    if (widget.items.isEmpty) {
      return widget.emptyBuilder?.call() ?? const SizedBox.shrink();
    }

    return ListView.builder(
      cacheExtent: widget.cacheExtent?.toDouble(),
      itemCount: widget.items.length,
      itemBuilder: (context, index) {
        final item = widget.items[index];
        return RepaintBoundary(
          child: widget.itemBuilder(context, item, index),
        );
      },
    );
  }
}

// 智能的图片Widget
class SmartImage extends StatefulWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;

  const SmartImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
  });

  @override
  State<SmartImage> createState() => _SmartImageState();
}

class _SmartImageState extends State<SmartImage> {
  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Uint8List?>(
      future: SmartImageCache.getImage(widget.imageUrl),
      builder: (context, snapshot) {
        if (snapshot.hasData && snapshot.data != null) {
          return Image.memory(
            snapshot.data!,
            width: widget.width,
            height: widget.height,
            fit: widget.fit,
            gaplessPlayback: true, // 避免闪烁
          );
        }

        if (snapshot.hasError) {
          return widget.errorWidget ??
                 const Icon(Icons.error, color: Colors.red);
        }

        return widget.placeholder ??
               const Center(child: CircularProgressIndicator());
      },
    );
  }
}
```

#### 2. 计算性能优化

```dart
// 计算结果缓存
class ComputationCache {
  static final Map<String, dynamic> _cache = {};

  static T getOrCompute<T>(
    String key,
    T Function() computation,
  ) {
    if (_cache.containsKey(key)) {
      return _cache[key] as T;
    }

    final result = computation();
    _cache[key] = result;
    return result;
  }

  static void invalidate(String key) {
    _cache.remove(key);
  }

  static void clear() {
    _cache.clear();
  }
}

// 防抖和节流工具
class Debouncer {
  final Duration delay;
  Timer? _timer;

  Debouncer({required this.delay});

  void run(VoidCallback action) {
    _timer?.cancel();
    _timer = Timer(delay, action);
  }

  void dispose() {
    _timer?.cancel();
  }
}

class Throttler {
  final Duration duration;
  DateTime? _lastActionTime;

  Throttler({required this.duration});

  void run(VoidCallback action) {
    final now = DateTime.now();
    if (_lastActionTime == null ||
        now.difference(_lastActionTime!) >= duration) {
      _lastActionTime = now;
      action();
    }
  }
}

// 优化的搜索Widget
class OptimizedSearchField extends StatefulWidget {
  final Function(String) onSearch;
  final Duration debounceDelay;

  const OptimizedSearchField({
    super.key,
    required this.onSearch,
    this.debounceDelay = const Duration(milliseconds: 300),
  });

  @override
  State<OptimizedSearchField> createState() => _OptimizedSearchFieldState();
}

class _OptimizedSearchFieldState extends State<OptimizedSearchField> {
  late final Debouncer _debouncer;
  final TextEditingController _controller = TextEditingController();

  @override
  void initState() {
    super.initState();
    _debouncer = Debouncer(delay: widget.debounceDelay);

    _controller.addListener(() {
      _debouncer.run(() {
        widget.onSearch(_controller.text);
      });
    });
  }

  @override
  void dispose() {
    _debouncer.dispose();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: _controller,
      decoration: const InputDecoration(
        hintText: '搜索...',
        prefixIcon: Icon(Icons.search),
      ),
    );
  }
}
```


---

## 第二十二章：测试与调试

### 🧪 测试策略概览

在Dart和Flutter开发中，测试是确保代码质量的重要环节。我们需要建立完整的测试金字塔。

#### 测试金字塔

```dart
// 测试金字塔结构
/*
    /\     E2E Tests (端到端测试)
   /  \    - 真实用户场景
  /____\   - 完整应用流程
 /      \
/________\  Integration Tests (集成测试)
           - 模块间交互测试
           - Widget与服务集成

           Unit Tests (单元测试)
           - 函数、类的独立测试
           - 业务逻辑验证
           - 最多数量，最快执行
*/
```

### 🔧 单元测试最佳实践

#### 1. 基础单元测试

```dart
// test/unit/models/user_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/models/user.dart';

void main() {
  group('User Model Tests', () {
    late User testUser;

    setUp(() {
      testUser = User(
        id: '123',
        name: '张三',
        email: '<EMAIL>',
        age: 25,
      );
    });

    group('构造函数测试', () {
      test('应该正确创建用户对象', () {
        expect(testUser.id, equals('123'));
        expect(testUser.name, equals('张三'));
        expect(testUser.email, equals('<EMAIL>'));
        expect(testUser.age, equals(25));
      });

      test('应该正确创建匿名用户', () {
        final anonymousUser = User.anonymous();

        expect(anonymousUser.id, isEmpty);
        expect(anonymousUser.name, equals('Anonymous'));
        expect(anonymousUser.email, isEmpty);
        expect(anonymousUser.age, equals(0));
      });
    });

    group('方法测试', () {
      test('celebrateBirthday应该增加年龄', () {
        final initialAge = testUser.age;
        testUser.celebrateBirthday();

        expect(testUser.age, equals(initialAge + 1));
      });

      test('toString应该返回正确格式', () {
        final userString = testUser.toString();

        expect(userString, equals('User(张三, <EMAIL>)'));
      });
    });

    group('边界条件测试', () {
      test('年龄不能为负数', () {
        expect(
          () => User(
            id: '1',
            name: 'Test',
            email: '<EMAIL>',
            age: -1,
          ),
          throwsArgumentError,
        );
      });
    });
  });
}
```

#### 2. 服务层测试

```dart
// test/unit/services/user_service_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:myapp/services/user_service.dart';
import 'package:myapp/repositories/user_repository.dart';
import 'package:myapp/models/user.dart';

import 'user_service_test.mocks.dart';

@GenerateMocks([UserRepository])
void main() {
  group('UserService Tests', () {
    late UserService userService;
    late MockUserRepository mockRepository;

    setUp(() {
      mockRepository = MockUserRepository();
      userService = UserService(mockRepository);
    });

    group('获取用户', () {
      test('应该成功返回用户数据', () async {
        // Arrange
        const userId = '123';
        final expectedUser = User(
          id: userId,
          name: '张三',
          email: '<EMAIL>',
          age: 25,
        );

        when(mockRepository.getUserById(any))
            .thenAnswer((_) async => Result.success(expectedUser));

        // Act
        final result = await userService.getUser(userId);

        // Assert
        expect(result.isSuccess, true);
        expect(result.data, equals(expectedUser));
        verify(mockRepository.getUserById(userId)).called(1);
      });

      test('应该处理用户不存在的情况', () async {
        // Arrange
        const userId = 'nonexistent';

        when(mockRepository.getUserById(any))
            .thenAnswer((_) async => Result.failure('用户不存在'));

        // Act
        final result = await userService.getUser(userId);

        // Assert
        expect(result.isFailure, true);
        expect(result.error, equals('用户不存在'));
      });

      test('应该验证用户ID参数', () async {
        // Act & Assert
        expect(
          () => userService.getUser(''),
          throwsArgumentError,
        );
      });
    });

    group('创建用户', () {
      test('应该成功创建用户', () async {
        // Arrange
        final newUser = User(
          id: '456',
          name: '李四',
          email: '<EMAIL>',
          age: 30,
        );

        when(mockRepository.createUser(any))
            .thenAnswer((_) async => Result.success(newUser));

        // Act
        final result = await userService.createUser(
          name: '李四',
          email: '<EMAIL>',
          age: 30,
        );

        // Assert
        expect(result.isSuccess, true);
        expect(result.data?.name, equals('李四'));
        verify(mockRepository.createUser(any)).called(1);
      });

      test('应该验证邮箱格式', () async {
        // Act & Assert
        final result = await userService.createUser(
          name: '测试用户',
          email: 'invalid-email',
          age: 25,
        );

        expect(result.isFailure, true);
        expect(result.error, contains('邮箱格式不正确'));
        verifyNever(mockRepository.createUser(any));
      });
    });
  });
}
```

### 🎯 Widget测试

#### 1. 基础Widget测试

```dart
// test/widget/login_form_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:myapp/widgets/login_form.dart';

void main() {
  group('LoginForm Widget Tests', () {
    testWidgets('应该显示登录表单元素', (WidgetTester tester) async {
      // Arrange
      bool loginCalled = false;
      void onLogin(String email, String password) {
        loginCalled = true;
      }

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: LoginForm(onLogin: onLogin),
          ),
        ),
      );

      // Assert
      expect(find.byType(TextFormField), findsNWidgets(2)); // 邮箱和密码字段
      expect(find.text('邮箱'), findsOneWidget);
      expect(find.text('密码'), findsOneWidget);
      expect(find.byType(ElevatedButton), findsOneWidget);
      expect(find.text('登录'), findsOneWidget);
    });

    testWidgets('应该验证必填字段', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: LoginForm(onLogin: (_, __) {}),
          ),
        ),
      );

      // Act - 点击登录按钮而不填写任何字段
      await tester.tap(find.byType(ElevatedButton));
      await tester.pump();

      // Assert
      expect(find.text('请输入邮箱'), findsOneWidget);
      expect(find.text('请输入密码'), findsOneWidget);
    });

    testWidgets('应该验证邮箱格式', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: LoginForm(onLogin: (_, __) {}),
          ),
        ),
      );

      // Act
      await tester.enterText(
        find.byKey(const Key('emailField')),
        'invalid-email',
      );
      await tester.enterText(
        find.byKey(const Key('passwordField')),
        'password123',
      );
      await tester.tap(find.byType(ElevatedButton));
      await tester.pump();

      // Assert
      expect(find.text('邮箱格式不正确'), findsOneWidget);
    });

    testWidgets('应该成功提交表单', (WidgetTester tester) async {
      // Arrange
      String? submittedEmail;
      String? submittedPassword;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: LoginForm(
              onLogin: (email, password) {
                submittedEmail = email;
                submittedPassword = password;
              },
            ),
          ),
        ),
      );

      // Act
      await tester.enterText(
        find.byKey(const Key('emailField')),
        '<EMAIL>',
      );
      await tester.enterText(
        find.byKey(const Key('passwordField')),
        'password123',
      );
      await tester.tap(find.byType(ElevatedButton));
      await tester.pump();

      // Assert
      expect(submittedEmail, equals('<EMAIL>'));
      expect(submittedPassword, equals('password123'));
    });
  });
}
```

#### 2. 复杂Widget测试

```dart
// test/widget/user_profile_widget_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:provider/provider.dart';
import 'package:myapp/widgets/user_profile_widget.dart';
import 'package:myapp/services/user_service.dart';
import 'package:myapp/models/user.dart';

import '../unit/services/user_service_test.mocks.dart';

void main() {
  group('UserProfileWidget Tests', () {
    late MockUserService mockUserService;

    setUp(() {
      mockUserService = MockUserService();
    });

    Widget createTestWidget({required String userId}) {
      return MaterialApp(
        home: Provider<UserService>.value(
          value: mockUserService,
          child: UserProfileWidget(userId: userId),
        ),
      );
    }

    testWidgets('应该显示加载状态', (WidgetTester tester) async {
      // Arrange
      when(mockUserService.getUser(any))
          .thenAnswer((_) async => Future.delayed(
                const Duration(seconds: 1),
                () => Result.success(User(
                  id: '123',
                  name: '张三',
                  email: '<EMAIL>',
                  age: 25,
                )),
              ));

      // Act
      await tester.pumpWidget(createTestWidget(userId: '123'));

      // Assert
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('加载中...'), findsOneWidget);
    });

    testWidgets('应该显示用户信息', (WidgetTester tester) async {
      // Arrange
      final testUser = User(
        id: '123',
        name: '张三',
        email: '<EMAIL>',
        age: 25,
      );

      when(mockUserService.getUser(any))
          .thenAnswer((_) async => Result.success(testUser));

      // Act
      await tester.pumpWidget(createTestWidget(userId: '123'));
      await tester.pumpAndSettle(); // 等待异步操作完成

      // Assert
      expect(find.text('张三'), findsOneWidget);
      expect(find.text('<EMAIL>'), findsOneWidget);
      expect(find.text('25岁'), findsOneWidget);
      expect(find.byType(CircleAvatar), findsOneWidget);
    });

    testWidgets('应该显示错误状态', (WidgetTester tester) async {
      // Arrange
      when(mockUserService.getUser(any))
          .thenAnswer((_) async => Result.failure('用户不存在'));

      // Act
      await tester.pumpWidget(createTestWidget(userId: 'nonexistent'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.byIcon(Icons.error), findsOneWidget);
      expect(find.text('用户不存在'), findsOneWidget);
      expect(find.text('重试'), findsOneWidget);
    });

    testWidgets('应该支持重试功能', (WidgetTester tester) async {
      // Arrange
      when(mockUserService.getUser(any))
          .thenAnswer((_) async => Result.failure('网络错误'));

      await tester.pumpWidget(createTestWidget(userId: '123'));
      await tester.pumpAndSettle();

      // 模拟重试成功
      final testUser = User(
        id: '123',
        name: '张三',
        email: '<EMAIL>',
        age: 25,
      );
      when(mockUserService.getUser(any))
          .thenAnswer((_) async => Result.success(testUser));

      // Act
      await tester.tap(find.text('重试'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('张三'), findsOneWidget);
      verify(mockUserService.getUser('123')).called(2); // 初始调用 + 重试
    });
  });
}
```

### 🔗 集成测试

#### 1. 完整功能流程测试

```dart
// test/integration/user_management_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:myapp/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('用户管理集成测试', () {
    testWidgets('完整的用户注册登录流程', (WidgetTester tester) async {
      // 启动应用
      app.main();
      await tester.pumpAndSettle();

      // 1. 导航到注册页面
      await tester.tap(find.text('注册'));
      await tester.pumpAndSettle();

      // 2. 填写注册信息
      await tester.enterText(
        find.byKey(const Key('nameField')),
        '测试用户',
      );
      await tester.enterText(
        find.byKey(const Key('emailField')),
        '<EMAIL>',
      );
      await tester.enterText(
        find.byKey(const Key('passwordField')),
        'password123',
      );
      await tester.enterText(
        find.byKey(const Key('confirmPasswordField')),
        'password123',
      );

      // 3. 提交注册
      await tester.tap(find.text('注册'));
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // 4. 验证注册成功并跳转到登录页
      expect(find.text('注册成功'), findsOneWidget);
      await tester.tap(find.text('确认'));
      await tester.pumpAndSettle();

      // 5. 登录
      await tester.enterText(
        find.byKey(const Key('loginEmailField')),
        '<EMAIL>',
      );
      await tester.enterText(
        find.byKey(const Key('loginPasswordField')),
        'password123',
      );
      await tester.tap(find.text('登录'));
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // 6. 验证登录成功并进入主页
      expect(find.text('欢迎，测试用户'), findsOneWidget);
      expect(find.byKey(const Key('homeScreen')), findsOneWidget);
    });

    testWidgets('用户资料编辑流程', (WidgetTester tester) async {
      // 假设用户已经登录
      app.main();
      await tester.pumpAndSettle();

      // 1. 导航到用户资料页面
      await tester.tap(find.byIcon(Icons.person));
      await tester.pumpAndSettle();

      // 2. 进入编辑模式
      await tester.tap(find.text('编辑'));
      await tester.pumpAndSettle();

      // 3. 修改信息
      await tester.enterText(
        find.byKey(const Key('nameField')),
        '新用户名',
      );
      await tester.enterText(
        find.byKey(const Key('bioField')),
        '这是我的新简介',
      );

      // 4. 保存修改
      await tester.tap(find.text('保存'));
      await tester.pumpAndSettle(const Duration(seconds: 2));

      // 5. 验证修改成功
      expect(find.text('新用户名'), findsOneWidget);
      expect(find.text('这是我的新简介'), findsOneWidget);
      expect(find.text('资料更新成功'), findsOneWidget);
    });
  });
}
```

### 🐛 调试技巧

#### 1. 调试工具使用

```dart
// 调试工具类
class DebugHelper {
  static bool get isDebugMode => kDebugMode;

  // 性能监控
  static void measurePerformance(String name, Function() operation) {
    if (!isDebugMode) {
      operation();
      return;
    }

    final stopwatch = Stopwatch()..start();
    operation();
    stopwatch.stop();

    debugPrint('⏱️ $name 执行时间: ${stopwatch.elapsed.inMilliseconds}ms');
  }

  // 内存监控
  static void logMemoryUsage(String tag) {
    if (!isDebugMode) return;

    final info = ProcessInfo.currentRss;
    debugPrint('🧠 [$tag] 内存使用: ${(info / 1024 / 1024).toStringAsFixed(2)}MB');
  }

  // 网络请求调试
  static void logNetworkRequest(String method, String url, {
    Map<String, dynamic>? headers,
    dynamic body,
  }) {
    if (!isDebugMode) return;

    debugPrint('🌐 $method $url');
    if (headers != null) {
      debugPrint('📋 Headers: $headers');
    }
    if (body != null) {
      debugPrint('📦 Body: $body');
    }
  }

  // 状态变化调试
  static void logStateChange(String widget, String from, String to) {
    if (!isDebugMode) return;

    debugPrint('🔄 [$widget] State: $from -> $to');
  }
}

// 使用示例
class UserService {
  Future<User> getUser(String id) async {
    return DebugHelper.measurePerformance('GetUser', () async {
      DebugHelper.logNetworkRequest('GET', '/users/$id');

      final user = await _fetchUser(id);

      DebugHelper.logMemoryUsage('After GetUser');
      return user;
    });
  }
}
```

#### 2. 错误追踪

```dart
// 错误追踪系统
class ErrorTracker {
  static final List<AppError> _errors = [];

  static void trackError(AppError error) {
    _errors.add(error);

    if (kDebugMode) {
      _printError(error);
    }

    // 在生产环境中发送到监控服务
    if (kReleaseMode) {
      _sendToMonitoring(error);
    }
  }

  static void _printError(AppError error) {
    print('''
🚨 ERROR DETECTED 🚨
Time: ${error.timestamp}
Type: ${error.type}
Message: ${error.message}
Stack Trace:
${error.stackTrace}
User Actions: ${error.userActions.join(' -> ')}
---
''');
  }

  static List<AppError> getRecentErrors({int limit = 10}) {
    return _errors.take(limit).toList();
  }

  static void clearErrors() {
    _errors.clear();
  }
}

class AppError {
  final DateTime timestamp;
  final String type;
  final String message;
  final StackTrace? stackTrace;
  final List<String> userActions;

  AppError({
    required this.type,
    required this.message,
    this.stackTrace,
    List<String>? userActions,
  }) : timestamp = DateTime.now(),
       userActions = userActions ?? [];
}

// 用户行为追踪
class UserActionTracker {
  static final List<String> _actions = [];

  static void track(String action) {
    _actions.add('${DateTime.now()}: $action');

    // 只保留最近的20个操作
    if (_actions.length > 20) {
      _actions.removeAt(0);
    }

    if (kDebugMode) {
      debugPrint('👤 User Action: $action');
    }
  }

  static List<String> getRecentActions() => List.from(_actions);
}
```

### 📊 测试覆盖率

#### 1. 覆盖率监控

```bash
# 生成测试覆盖率报告
flutter test --coverage

# 生成HTML报告
genhtml coverage/lcov.info -o coverage/html

# 查看覆盖率
open coverage/html/index.html
```

#### 2. 覆盖率配置

```yaml
# test/coverage_helper_test.dart
// Helper file to make coverage work for all files
// ignore_for_file: unused_import
import 'package:myapp/main.dart';
import 'package:myapp/models/user.dart';
import 'package:myapp/services/user_service.dart';
import 'package:myapp/widgets/login_form.dart';
// ... 导入所有需要测试覆盖率的文件

void main() {
  // 这个文件不包含实际测试，只是为了确保覆盖率统计包含所有文件
}
```

### 🎯 最佳实践总结

#### 1. 测试组织原则

```dart
// 1. 使用描述性测试名称
test('should return user when valid ID is provided', () {
  // 测试内容
});

// 2. 使用AAA模式 (Arrange, Act, Assert)
test('should calculate discount correctly', () {
  // Arrange
  final price = 100.0;
  final discountPercent = 0.1;

  // Act
  final result = calculateDiscount(price, discountPercent);

  // Assert
  expect(result, equals(90.0));
});

// 3. 测试边界条件
group('边界条件测试', () {
  test('should handle empty input', () {
    expect(() => processData([]), returnsNormally);
  });

  test('should handle null input', () {
    expect(() => processData(null), throwsArgumentError);
  });

  test('should handle maximum input', () {
    final maxInput = List.filled(1000, 'data');
    expect(() => processData(maxInput), returnsNormally);
  });
});
```

#### 2. Mock使用指南

```dart
// 好的Mock使用
class MockUserRepository extends Mock implements UserRepository {}

test('should call repository with correct parameters', () async {
  // Arrange
  final mockRepo = MockUserRepository();
  final service = UserService(mockRepo);

  when(mockRepo.getUser(any))
      .thenAnswer((_) async => testUser);

  // Act
  await service.getUser('123');

  // Assert
  verify(mockRepo.getUser('123')).called(1);
});

// 避免过度Mock
test('should not mock value objects', () {
  // ❌ 不要mock简单的值对象
  // final mockUser = MockUser();

  // ✅ 直接创建真实对象
  final user = User(id: '1', name: 'Test');
  expect(user.name, equals('Test'));
});
```


---

## 第二十三章：完整项目实战

### 🏗️ 项目架构设计

#### 1. 项目需求分析

我们将构建一个完整的任务管理应用，包含以下功能：

- 用户认证（注册、登录、登出）
- 任务管理（创建、编辑、删除、标记完成）
- 分类管理（项目分组）
- 数据同步（本地存储 + 云端同步）
- 离线支持

#### 2. 技术栈选择

```dart
// 核心依赖清单
dependencies:
  # 框架核心
  flutter: ^3.10.0

  # 状态管理
  flutter_bloc: ^8.1.3

  # 网络请求
  dio: ^5.3.2

  # 本地存储
  sqflite: ^2.3.0
  shared_preferences: ^2.2.0

  # 依赖注入
  get_it: ^7.6.0
  injectable: ^2.3.0

  # 数据模型
  json_annotation: ^4.8.1
  freezed_annotation: ^2.4.1

  # 工具类
  equatable: ^2.0.5
  dartz: ^0.10.1

dev_dependencies:
  # 代码生成
  build_runner: ^2.4.6
  json_serializable: ^6.7.1
  freezed: ^2.4.5
  injectable_generator: ^2.4.0

  # 测试
  flutter_test:
    sdk: flutter
  mockito: ^5.4.2
  integration_test:
    sdk: flutter
```

### 🎯 核心业务实现

#### 1. 数据模型定义

```dart
// lib/domain/entities/task.dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'task.freezed.dart';

@freezed
class Task with _$Task {
  const factory Task({
    required String id,
    required String title,
    required String description,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? dueDate,
    @Default(TaskStatus.pending) TaskStatus status,
    @Default(TaskPriority.medium) TaskPriority priority,
    String? projectId,
    @Default([]) List<String> tags,
  }) = _Task;
}

enum TaskStatus {
  pending,
  inProgress,
  completed,
  cancelled,
}

enum TaskPriority {
  low,
  medium,
  high,
  urgent,
}

// lib/domain/entities/project.dart
@freezed
class Project with _$Project {
  const factory Project({
    required String id,
    required String name,
    required String description,
    required DateTime createdAt,
    required String userId,
    @Default(ProjectColor.blue) ProjectColor color,
    @Default([]) List<String> taskIds,
  }) = _Project;
}

enum ProjectColor {
  blue,
  green,
  red,
  purple,
  orange,
  pink,
}

// lib/domain/entities/user.dart
@freezed
class User with _$User {
  const factory User({
    required String id,
    required String email,
    required String name,
    String? avatar,
    required DateTime createdAt,
    DateTime? lastLoginAt,
  }) = _User;
}
```

#### 2. 仓库模式实现

```dart
// lib/domain/repositories/task_repository.dart
abstract class TaskRepository {
  Future<Either<Failure, List<Task>>> getTasks({String? projectId});
  Future<Either<Failure, Task>> getTaskById(String id);
  Future<Either<Failure, Task>> createTask(CreateTaskRequest request);
  Future<Either<Failure, Task>> updateTask(String id, UpdateTaskRequest request);
  Future<Either<Failure, void>> deleteTask(String id);
  Future<Either<Failure, void>> syncTasks();
}

// lib/data/repositories/task_repository_impl.dart
@LazySingleton(as: TaskRepository)
class TaskRepositoryImpl implements TaskRepository {
  final TaskRemoteDataSource _remoteDataSource;
  final TaskLocalDataSource _localDataSource;
  final NetworkInfo _networkInfo;

  const TaskRepositoryImpl(
    this._remoteDataSource,
    this._localDataSource,
    this._networkInfo,
  );

  @override
  Future<Either<Failure, List<Task>>> getTasks({String? projectId}) async {
    try {
      if (await _networkInfo.isConnected) {
        // 在线模式：从远程获取并缓存
        final remoteTasks = await _remoteDataSource.getTasks(projectId: projectId);
        await _localDataSource.cacheTasks(remoteTasks);
        return Right(remoteTasks.map((model) => model.toEntity()).toList());
      } else {
        // 离线模式：从本地缓存获取
        final localTasks = await _localDataSource.getCachedTasks(projectId: projectId);
        return Right(localTasks.map((model) => model.toEntity()).toList());
      }
    } catch (e) {
      return Left(_mapException(e));
    }
  }

  @override
  Future<Either<Failure, Task>> createTask(CreateTaskRequest request) async {
    try {
      final taskModel = TaskModel.fromCreateRequest(request);

      // 先保存到本地
      await _localDataSource.saveTask(taskModel);

      if (await _networkInfo.isConnected) {
        try {
          // 尝试同步到远程
          final remoteTask = await _remoteDataSource.createTask(request);
          await _localDataSource.updateTask(remoteTask);
          return Right(remoteTask.toEntity());
        } catch (e) {
          // 远程失败但本地已保存，标记为待同步
          await _localDataSource.markForSync(taskModel.id);
          return Right(taskModel.toEntity());
        }
      } else {
        // 离线模式，标记为待同步
        await _localDataSource.markForSync(taskModel.id);
        return Right(taskModel.toEntity());
      }
    } catch (e) {
      return Left(_mapException(e));
    }
  }

  @override
  Future<Either<Failure, void>> syncTasks() async {
    if (!await _networkInfo.isConnected) {
      return const Left(NetworkFailure('No internet connection'));
    }

    try {
      // 获取待同步的任务
      final pendingTasks = await _localDataSource.getPendingSyncTasks();

      // 批量同步到远程
      for (final task in pendingTasks) {
        try {
          if (task.isNew) {
            await _remoteDataSource.createTask(task.toCreateRequest());
          } else if (task.isModified) {
            await _remoteDataSource.updateTask(task.id, task.toUpdateRequest());
          } else if (task.isDeleted) {
            await _remoteDataSource.deleteTask(task.id);
          }

          await _localDataSource.markAsSynced(task.id);
        } catch (e) {
          // 记录同步失败的任务
          print('Failed to sync task ${task.id}: $e');
        }
      }

      return const Right(null);
    } catch (e) {
      return Left(_mapException(e));
    }
  }

  Failure _mapException(dynamic error) {
    if (error is DioException) {
      switch (error.response?.statusCode) {
        case 400:
          return const ValidationFailure('Invalid request data');
        case 401:
          return const AuthFailure('Authentication required');
        case 404:
          return const NotFoundFailure('Task not found');
        case 500:
          return const ServerFailure('Internal server error');
        default:
          return NetworkFailure('Network error: ${error.message}');
      }
    } else if (error is SqliteException) {
      return DatabaseFailure('Database error: ${error.message}');
    }

    return UnknownFailure('Unknown error: $error');
  }
}
```

#### 3. 数据源实现

```dart
// lib/data/datasources/task_local_data_source.dart
@LazySingleton()
class TaskLocalDataSource {
  final Database _database;

  const TaskLocalDataSource(this._database);

  Future<List<TaskModel>> getCachedTasks({String? projectId}) async {
    final whereClause = projectId != null ? 'WHERE project_id = ?' : '';
    final whereArgs = projectId != null ? [projectId] : null;

    final maps = await _database.query(
      'tasks',
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'created_at DESC',
    );

    return maps.map((map) => TaskModel.fromJson(map)).toList();
  }

  Future<void> saveTask(TaskModel task) async {
    await _database.insert(
      'tasks',
      task.toJson(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<void> cacheTasks(List<TaskModel> tasks) async {
    final batch = _database.batch();

    for (final task in tasks) {
      batch.insert(
        'tasks',
        task.toJson(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }

    await batch.commit();
  }

  Future<void> markForSync(String taskId) async {
    await _database.update(
      'tasks',
      {'sync_status': 'pending'},
      where: 'id = ?',
      whereArgs: [taskId],
    );
  }

  Future<List<TaskModel>> getPendingSyncTasks() async {
    final maps = await _database.query(
      'tasks',
      where: 'sync_status = ?',
      whereArgs: ['pending'],
    );

    return maps.map((map) => TaskModel.fromJson(map)).toList();
  }
}

// lib/data/datasources/task_remote_data_source.dart
@LazySingleton()
class TaskRemoteDataSource {
  final Dio _dio;
  final String _baseUrl;

  const TaskRemoteDataSource(this._dio, @Named('baseUrl') this._baseUrl);

  Future<List<TaskModel>> getTasks({String? projectId}) async {
    final response = await _dio.get(
      '$_baseUrl/tasks',
      queryParameters: projectId != null ? {'project_id': projectId} : null,
    );

    if (response.statusCode == 200) {
      final List<dynamic> data = response.data['data'];
      return data.map((json) => TaskModel.fromJson(json)).toList();
    }

    throw DioException(
      requestOptions: response.requestOptions,
      response: response,
    );
  }

  Future<TaskModel> createTask(CreateTaskRequest request) async {
    final response = await _dio.post(
      '$_baseUrl/tasks',
      data: request.toJson(),
    );

    if (response.statusCode == 201) {
      return TaskModel.fromJson(response.data['data']);
    }

    throw DioException(
      requestOptions: response.requestOptions,
      response: response,
    );
  }

  Future<TaskModel> updateTask(String id, UpdateTaskRequest request) async {
    final response = await _dio.put(
      '$_baseUrl/tasks/$id',
      data: request.toJson(),
    );

    if (response.statusCode == 200) {
      return TaskModel.fromJson(response.data['data']);
    }

    throw DioException(
      requestOptions: response.requestOptions,
      response: response,
    );
  }

  Future<void> deleteTask(String id) async {
    final response = await _dio.delete('$_baseUrl/tasks/$id');

    if (response.statusCode != 204) {
      throw DioException(
        requestOptions: response.requestOptions,
        response: response,
      );
    }
  }
}
```

#### 4. 业务逻辑层（UseCase）

```dart
// lib/domain/usecases/create_task_usecase.dart
@LazySingleton()
class CreateTaskUseCase {
  final TaskRepository _repository;
  final Validator _validator;

  const CreateTaskUseCase(this._repository, this._validator);

  Future<Either<Failure, Task>> call(CreateTaskParams params) async {
    // 1. 参数验证
    final validationResult = _validator.validateCreateTaskParams(params);
    if (validationResult.isLeft()) {
      return Left(validationResult.fold((l) => l, (r) => throw Exception()));
    }

    // 2. 创建请求对象
    final request = CreateTaskRequest(
      title: params.title,
      description: params.description,
      dueDate: params.dueDate,
      priority: params.priority,
      projectId: params.projectId,
      tags: params.tags,
    );

    // 3. 调用仓库创建任务
    final result = await _repository.createTask(request);

    // 4. 处理结果
    return result.fold(
      (failure) => Left(failure),
      (task) {
        // 任务创建成功后可以触发其他业务逻辑
        _trackTaskCreation(task);
        return Right(task);
      },
    );
  }

  void _trackTaskCreation(Task task) {
    // 统计分析
    AnalyticsService.track('task_created', {
      'priority': task.priority.name,
      'has_due_date': task.dueDate != null,
      'has_project': task.projectId != null,
      'tags_count': task.tags.length,
    });
  }
}

@freezed
class CreateTaskParams with _$CreateTaskParams {
  const factory CreateTaskParams({
    required String title,
    @Default('') String description,
    DateTime? dueDate,
    @Default(TaskPriority.medium) TaskPriority priority,
    String? projectId,
    @Default([]) List<String> tags,
  }) = _CreateTaskParams;
}

// lib/domain/usecases/get_tasks_usecase.dart
@LazySingleton()
class GetTasksUseCase {
  final TaskRepository _repository;

  const GetTasksUseCase(this._repository);

  Future<Either<Failure, List<Task>>> call(GetTasksParams params) async {
    final result = await _repository.getTasks(projectId: params.projectId);

    return result.map((tasks) {
      // 应用过滤和排序
      var filteredTasks = tasks;

      if (params.status != null) {
        filteredTasks = filteredTasks
            .where((task) => task.status == params.status)
            .toList();
      }

      if (params.priority != null) {
        filteredTasks = filteredTasks
            .where((task) => task.priority == params.priority)
            .toList();
      }

      // 排序
      switch (params.sortBy) {
        case TaskSortBy.createdAt:
          filteredTasks.sort((a, b) => b.createdAt.compareTo(a.createdAt));
          break;
        case TaskSortBy.dueDate:
          filteredTasks.sort((a, b) {
            if (a.dueDate == null && b.dueDate == null) return 0;
            if (a.dueDate == null) return 1;
            if (b.dueDate == null) return -1;
            return a.dueDate!.compareTo(b.dueDate!);
          });
          break;
        case TaskSortBy.priority:
          filteredTasks.sort((a, b) =>
              b.priority.index.compareTo(a.priority.index));
          break;
      }

      return filteredTasks;
    });
  }
}
```

### 🎨 UI层实现

#### 1. BLoC状态管理

```dart
// lib/presentation/blocs/task/task_bloc.dart
@Injectable()
class TaskBloc extends Bloc<TaskEvent, TaskState> {
  final GetTasksUseCase _getTasksUseCase;
  final CreateTaskUseCase _createTaskUseCase;
  final UpdateTaskUseCase _updateTaskUseCase;
  final DeleteTaskUseCase _deleteTaskUseCase;
  final SyncTasksUseCase _syncTasksUseCase;

  TaskBloc(
    this._getTasksUseCase,
    this._createTaskUseCase,
    this._updateTaskUseCase,
    this._deleteTaskUseCase,
    this._syncTasksUseCase,
  ) : super(const TaskState()) {
    on<TasksRequested>(_onTasksRequested);
    on<TaskCreated>(_onTaskCreated);
    on<TaskUpdated>(_onTaskUpdated);
    on<TaskDeleted>(_onTaskDeleted);
    on<TasksSynced>(_onTasksSynced);
  }

  Future<void> _onTasksRequested(
    TasksRequested event,
    Emitter<TaskState> emit,
  ) async {
    emit(state.copyWith(status: TaskStatus.loading));

    final result = await _getTasksUseCase(GetTasksParams(
      projectId: event.projectId,
      status: event.filterStatus,
      priority: event.filterPriority,
      sortBy: event.sortBy,
    ));

    result.fold(
      (failure) => emit(state.copyWith(
        status: TaskStatus.failure,
        errorMessage: failure.message,
      )),
      (tasks) => emit(state.copyWith(
        status: TaskStatus.success,
        tasks: tasks,
        filteredTasks: tasks,
      )),
    );
  }

  Future<void> _onTaskCreated(
    TaskCreated event,
    Emitter<TaskState> emit,
  ) async {
    final result = await _createTaskUseCase(event.params);

    result.fold(
      (failure) => emit(state.copyWith(
        status: TaskStatus.failure,
        errorMessage: failure.message,
      )),
      (task) {
        final updatedTasks = [task, ...state.tasks];
        emit(state.copyWith(
          tasks: updatedTasks,
          filteredTasks: updatedTasks,
        ));
      },
    );
  }

  Future<void> _onTasksSynced(
    TasksSynced event,
    Emitter<TaskState> emit,
  ) async {
    emit(state.copyWith(isSyncing: true));

    final result = await _syncTasksUseCase(NoParams());

    result.fold(
      (failure) => emit(state.copyWith(
        isSyncing: false,
        errorMessage: 'Sync failed: ${failure.message}',
      )),
      (_) {
        emit(state.copyWith(isSyncing: false));
        // 同步完成后重新加载任务
        add(const TasksRequested());
      },
    );
  }
}

// lib/presentation/blocs/task/task_state.dart
@freezed
class TaskState with _$TaskState {
  const factory TaskState({
    @Default(TaskStatus.initial) TaskStatus status,
    @Default([]) List<Task> tasks,
    @Default([]) List<Task> filteredTasks,
    @Default(false) bool isSyncing,
    String? errorMessage,
  }) = _TaskState;
}

enum TaskStatus {
  initial,
  loading,
  success,
  failure,
}

// lib/presentation/blocs/task/task_event.dart
@freezed
class TaskEvent with _$TaskEvent {
  const factory TaskEvent.tasksRequested({
    String? projectId,
    TaskStatus? filterStatus,
    TaskPriority? filterPriority,
    @Default(TaskSortBy.createdAt) TaskSortBy sortBy,
  }) = TasksRequested;

  const factory TaskEvent.taskCreated(CreateTaskParams params) = TaskCreated;
  const factory TaskEvent.taskUpdated(String id, UpdateTaskParams params) = TaskUpdated;
  const factory TaskEvent.taskDeleted(String id) = TaskDeleted;
  const factory TaskEvent.tasksSynced() = TasksSynced;
}
```

#### 2. UI组件实现

```dart
// lib/presentation/pages/tasks/tasks_page.dart
class TasksPage extends StatelessWidget {
  const TasksPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<TaskBloc>()..add(const TasksRequested()),
      child: const TasksView(),
    );
  }
}

class TasksView extends StatelessWidget {
  const TasksView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('任务管理'),
        actions: [
          BlocBuilder<TaskBloc, TaskState>(
            buildWhen: (previous, current) => previous.isSyncing != current.isSyncing,
            builder: (context, state) {
              if (state.isSyncing) {
                return const Padding(
                  padding: EdgeInsets.all(16),
                  child: SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                );
              }

              return IconButton(
                icon: const Icon(Icons.sync),
                onPressed: () => context.read<TaskBloc>().add(const TasksSynced()),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterDialog(context),
          ),
        ],
      ),
      body: BlocConsumer<TaskBloc, TaskState>(
        listener: (context, state) {
          if (state.status == TaskStatus.failure && state.errorMessage != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.errorMessage!),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          switch (state.status) {
            case TaskStatus.initial:
            case TaskStatus.loading:
              return const Center(child: CircularProgressIndicator());

            case TaskStatus.failure:
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.error_outline, size: 64, color: Colors.red),
                    const SizedBox(height: 16),
                    Text(
                      state.errorMessage ?? '加载失败',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => context.read<TaskBloc>().add(const TasksRequested()),
                      child: const Text('重试'),
                    ),
                  ],
                ),
              );

            case TaskStatus.success:
              if (state.filteredTasks.isEmpty) {
                return const EmptyTasksWidget();
              }

              return RefreshIndicator(
                onRefresh: () async {
                  context.read<TaskBloc>().add(const TasksRequested());
                },
                child: TaskListView(tasks: state.filteredTasks),
              );
          }
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showCreateTaskDialog(context),
        child: const Icon(Icons.add),
      ),
    );
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const TaskFilterDialog(),
    );
  }

  void _showCreateTaskDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const CreateTaskDialog(),
    );
  }
}

// lib/presentation/widgets/task_list_view.dart
class TaskListView extends StatelessWidget {
  final List<Task> tasks;

  const TaskListView({super.key, required this.tasks});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: tasks.length,
      itemBuilder: (context, index) {
        final task = tasks[index];
        return TaskCard(
          key: ValueKey(task.id),
          task: task,
          onTap: () => _navigateToTaskDetail(context, task),
          onStatusChanged: (newStatus) => _updateTaskStatus(context, task, newStatus),
          onDeleted: () => _deleteTask(context, task),
        );
      },
    );
  }

  void _navigateToTaskDetail(BuildContext context, Task task) {
    Navigator.of(context).pushNamed('/task-detail', arguments: task.id);
  }

  void _updateTaskStatus(BuildContext context, Task task, TaskStatus newStatus) {
    context.read<TaskBloc>().add(TaskUpdated(
      task.id,
      UpdateTaskParams(status: newStatus),
    ));
  }

  void _deleteTask(BuildContext context, Task task) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除任务"${task.title}"吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<TaskBloc>().add(TaskDeleted(task.id));
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }
}
```


---

## 第二十四章：常见问题与解决方案

### ❓ 开发过程常见问题

#### 1. 空安全相关问题

**问题：空安全迁移后出现大量编译错误**

```dart
// ❌ 常见错误
String? name;
print(name.length); // 编译错误：The property 'length' can't be unconditionally accessed

// ✅ 正确解决方案
String? name;

// 方案1：空值检查
if (name != null) {
  print(name.length); // 类型提升为String
}

// 方案2：空值合并
print((name ?? '').length);

// 方案3：条件访问
print(name?.length ?? 0);

// 方案4：空断言（确定不为空时使用）
print(name!.length); // 谨慎使用，确保name不是null
```

**问题：late变量未初始化**

```dart
// ❌ 问题代码
class UserService {
  late String apiKey;

  void doSomething() {
    print(apiKey); // 运行时错误：LateInitializationError
  }
}

// ✅ 解决方案
class UserService {
  late String apiKey;

  UserService() {
    apiKey = loadApiKey(); // 在构造函数中初始化
  }

  void doSomething() {
    print(apiKey); // 安全使用
  }
}

// 或者使用可空类型
class UserService {
  String? apiKey;

  void doSomething() {
    final key = apiKey;
    if (key != null) {
      print(key);
    }
  }
}
```

#### 2. 异步编程问题

**问题：忘记使用await导致的逻辑错误**

```dart
// ❌ 问题代码
Future<void> processData() {
  final data = fetchData(); // 返回Future<String>，不是String
  print(data.length); // 编译错误
}

// ✅ 解决方案
Future<void> processData() async {
  final data = await fetchData(); // 正确获取String
  print(data.length);
}

// 或者使用then
Future<void> processData() {
  return fetchData().then((data) {
    print(data.length);
  });
}
```

**问题：并发执行时的状态管理**

```dart
// ❌ 问题代码
class DataService {
  bool isLoading = false;

  Future<void> loadData() async {
    if (isLoading) return;

    isLoading = true;
    await fetchData(); // 如果多次调用，可能导致状态错乱
    isLoading = false;
  }
}

// ✅ 解决方案
class DataService {
  Future<void>? _currentOperation;

  Future<void> loadData() async {
    // 如果已有操作在进行，返回当前操作
    if (_currentOperation != null) {
      return _currentOperation!;
    }

    _currentOperation = _performLoad();

    try {
      await _currentOperation!;
    } finally {
      _currentOperation = null;
    }
  }

  Future<void> _performLoad() async {
    await fetchData();
  }
}
```

#### 3. Widget生命周期问题

**问题：在disposed状态下调用setState**

```dart
// ❌ 问题代码
class MyWidget extends StatefulWidget {
  @override
  _MyWidgetState createState() => _MyWidgetState();
}

class _MyWidgetState extends State<MyWidget> {
  Timer? timer;

  @override
  void initState() {
    super.initState();
    timer = Timer.periodic(Duration(seconds: 1), (timer) {
      setState(() {
        // 如果Widget已被disposed，会抛出异常
      });
    });
  }
}

// ✅ 解决方案
class _MyWidgetState extends State<MyWidget> {
  Timer? timer;

  @override
  void initState() {
    super.initState();
    timer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (mounted) { // 检查Widget是否还在树中
        setState(() {
          // 安全的状态更新
        });
      }
    });
  }

  @override
  void dispose() {
    timer?.cancel(); // 清理资源
    super.dispose();
  }
}
```

### 🐛 运行时常见错误

#### 1. 内存泄漏问题

**问题：StreamSubscription未正确取消**

```dart
// ❌ 问题代码
class DataWidget extends StatefulWidget {
  @override
  _DataWidgetState createState() => _DataWidgetState();
}

class _DataWidgetState extends State<DataWidget> {
  @override
  void initState() {
    super.initState();
    // StreamSubscription没有保存引用，无法取消
    dataStream.listen((data) {
      setState(() {
        // 处理数据
      });
    });
  }
}

// ✅ 解决方案1：手动管理
class _DataWidgetState extends State<DataWidget> {
  StreamSubscription? subscription;

  @override
  void initState() {
    super.initState();
    subscription = dataStream.listen((data) {
      if (mounted) {
        setState(() {
          // 处理数据
        });
      }
    });
  }

  @override
  void dispose() {
    subscription?.cancel();
    super.dispose();
  }
}

// ✅ 解决方案2：使用StreamBuilder
class _DataWidgetState extends State<DataWidget> {
  @override
  Widget build(BuildContext context) {
    return StreamBuilder(
      stream: dataStream,
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          return Text('Error: ${snapshot.error}');
        }
        if (!snapshot.hasData) {
          return CircularProgressIndicator();
        }
        return Text('Data: ${snapshot.data}');
      },
    );
  }
}
```

#### 2. 性能问题

**问题：ListView中的Widget过度重建**

```dart
// ❌ 问题代码
class ItemList extends StatelessWidget {
  final List<Item> items;

  const ItemList({super.key, required this.items});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        return Container( // 每次都创建新的Widget
          child: Text(item.name),
        );
      },
    );
  }
}

// ✅ 解决方案1：使用const constructor
class ItemList extends StatelessWidget {
  final List<Item> items;

  const ItemList({super.key, required this.items});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        return ItemTile(item: item); // 独立的Widget
      },
    );
  }
}

class ItemTile extends StatelessWidget {
  final Item item;

  const ItemTile({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Text(item.name),
    );
  }
}

// ✅ 解决方案2：使用RepaintBoundary
class ItemList extends StatelessWidget {
  final List<Item> items;

  const ItemList({super.key, required this.items});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        return RepaintBoundary(
          child: ItemTile(item: item),
        );
      },
    );
  }
}
```

### 🔧 调试技巧

#### 1. 使用调试工具

```dart
// 调试辅助类
class DebugUtils {
  // 1. 性能测量
  static T measureTime<T>(String label, T Function() operation) {
    final stopwatch = Stopwatch()..start();
    final result = operation();
    stopwatch.stop();

    debugPrint('⏱️ $label: ${stopwatch.elapsed.inMilliseconds}ms');
    return result;
  }

  // 2. Widget重建监控
  static Widget buildTracker(String name, Widget child) {
    return _BuildTracker(name: name, child: child);
  }

  // 3. 内存使用监控
  static void logMemoryUsage(String tag) {
    if (kDebugMode) {
      // 获取内存使用情况（需要使用vm_service包）
      debugPrint('🧠 Memory usage at $tag');
    }
  }
}

class _BuildTracker extends StatelessWidget {
  final String name;
  final Widget child;

  const _BuildTracker({required this.name, required this.child});

  @override
  Widget build(BuildContext context) {
    debugPrint('🔄 Building $name');
    return child;
  }
}

// 使用示例
class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return DebugUtils.buildTracker('MyWidget',
      Container(
        child: DebugUtils.measureTime('ExpensiveOperation', () {
          return Text('Hello World');
        }),
      ),
    );
  }
}
```

#### 2. 错误边界

```dart
// 全局错误捕获
class ErrorBoundary extends StatefulWidget {
  final Widget child;
  final void Function(FlutterErrorDetails)? onError;

  const ErrorBoundary({
    super.key,
    required this.child,
    this.onError,
  });

  @override
  State<ErrorBoundary> createState() => _ErrorBoundaryState();
}

class _ErrorBoundaryState extends State<ErrorBoundary> {
  FlutterErrorDetails? errorDetails;

  @override
  void initState() {
    super.initState();

    // 捕获Flutter框架错误
    FlutterError.onError = (details) {
      setState(() {
        errorDetails = details;
      });

      widget.onError?.call(details);

      // 在debug模式下也显示在控制台
      if (kDebugMode) {
        FlutterError.presentError(details);
      }
    };
  }

  @override
  Widget build(BuildContext context) {
    if (errorDetails != null) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text(
                '应用出现了一个错误',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              if (kDebugMode) ...[
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    errorDetails!.exception.toString(),
                    style: const TextStyle(color: Colors.red),
                  ),
                ),
              ],
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    errorDetails = null;
                  });
                },
                child: const Text('重试'),
              ),
            ],
          ),
        ),
      );
    }

    return widget.child;
  }
}

// 在main.dart中使用
void main() {
  runApp(
    ErrorBoundary(
      onError: (details) {
        // 发送错误到监控服务
        CrashReportingService.report(details);
      },
      child: MyApp(),
    ),
  );
}
```

### 📊 性能优化清单

#### 1. Widget优化检查清单

```dart
// 性能优化检查清单
class PerformanceChecker {
  // ✅ 检查1：是否使用了const constructor
  static void checkConstUsage() {
    /*
    ✅ 好的实践：
    const Text('Hello')
    const SizedBox(height: 16)
    const Divider()

    ❌ 需要改进：
    Text('Hello')  // 应该是 const Text('Hello')
    SizedBox(height: 16)  // 应该是 const SizedBox(height: 16)
    */
  }

  // ✅ 检查2：是否过度使用Stateful Widget
  static void checkWidgetTypes() {
    /*
    问：这个Widget需要状态管理吗？

    ✅ 使用StatelessWidget的场景：
    - 显示静态内容
    - 依赖父级传入的数据
    - 不需要管理内部状态

    ✅ 使用StatefulWidget的场景：
    - 需要管理内部状态
    - 需要监听生命周期事件
    - 需要处理用户交互
    */
  }

  // ✅ 检查3：是否正确使用RepaintBoundary
  static Widget wrapWithRepaintBoundary(Widget child) {
    /*
    适合使用RepaintBoundary的场景：
    - 复杂的自定义绘制Widget
    - 列表中的独立项目
    - 动画Widget
    - 频繁更新的Widget
    */
    return RepaintBoundary(child: child);
  }

  // ✅ 检查4：List优化
  static Widget optimizedList<T>(List<T> items, Widget Function(T) itemBuilder) {
    return ListView.builder(
      itemCount: items.length,
      cacheExtent: 200, // 预加载范围
      itemBuilder: (context, index) {
        return RepaintBoundary(
          child: itemBuilder(items[index]),
        );
      },
    );
  }
}
```


---

## 第二十五章：工具链与开发环境

### 🛠️ 开发工具配置

#### 1. IDE配置优化

**VS Code配置**

```json
// .vscode/settings.json
{
  "dart.debugExternalPackageLibraries": false,
  "dart.debugSdkLibraries": false,
  "dart.lineLength": 100,
  "dart.insertArgumentPlaceholders": false,
  "dart.updateImportsOnRename": true,
  "dart.warnWhenEditingFilesOutsideWorkspace": true,
  "dart.analysisExcludedFolders": [
    "build/**",
    ".dart_tool/**"
  ],
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll": true,
    "source.organizeImports": true
  },
  "files.associations": {
    "*.dart": "dart"
  }
}

// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug (Development)",
      "request": "launch",
      "type": "dart",
      "program": "lib/main_development.dart",
      "args": ["--flavor", "dev"]
    },
    {
      "name": "Debug (Staging)",
      "request": "launch",
      "type": "dart",
      "program": "lib/main_staging.dart",
      "args": ["--flavor", "staging"]
    },
    {
      "name": "Debug (Production)",
      "request": "launch",
      "type": "dart",
      "program": "lib/main_production.dart",
      "args": ["--flavor", "prod"]
    }
  ]
}

// .vscode/extensions.json
{
  "recommendations": [
    "Dart-Code.dart-code",
    "Dart-Code.flutter",
    "alexisvt.flutter-snippets",
    "everettjf.pubspec-assist",
    "Nash.awesome-flutter-snippets",
    "robert-brunhage.flutter-riverpod-snippets"
  ]
}
```

**IntelliJ IDEA/Android Studio配置**

```xml
<!-- .idea/codeStyles/Project.xml -->
<component name="ProjectCodeStyleConfiguration">
  <code_scheme name="Project" version="173">
    <Dart>
      <option name="LINE_LENGTH" value="100" />
      <option name="USE_PUB_SPEC_YAML_FORMAT" value="true" />
    </Dart>
  </code_scheme>
</component>
```

#### 2. 代码质量工具

**Analysis Options配置**

```yaml
# analysis_options.yaml
include: package:very_good_analysis/analysis_options.yaml

analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
    - "**/*.mocks.dart"
    - "build/**"
    - "lib/generated/**"

  strong-mode:
    implicit-casts: false
    implicit-dynamic: false

  errors:
    # 将警告升级为错误
    missing_required_param: error
    missing_return: error
    must_be_immutable: error

    # 忽略某些规则
    todo: ignore
    fixme: ignore

linter:
  rules:
    # 启用额外的规则
    - always_put_required_named_parameters_first
    - avoid_classes_with_only_static_members
    - avoid_unused_constructor_parameters
    - cancel_subscriptions
    - close_sinks
    - literal_only_boolean_expressions
    - no_adjacent_strings_in_list
    - prefer_const_constructors_in_immutables
    - prefer_const_declarations
    - prefer_const_literals_to_create_immutables
    - sort_constructors_first
    - sort_unnamed_constructors_first
    - unnecessary_await_in_return
    - unnecessary_lambdas
    - unnecessary_parenthesis
    - unnecessary_statements

    # 禁用某些规则
    - avoid_print: false  # 在开发阶段允许使用print
```

#### 3. Git Hooks配置

```bash
#!/bin/sh
# .git/hooks/pre-commit

echo "Running pre-commit checks..."

# 1. 格式化代码
echo "Formatting code..."
dart format lib/ test/ --set-exit-if-changed

if [ $? -ne 0 ]; then
  echo "❌ Code formatting failed. Please run 'dart format lib/ test/' and try again."
  exit 1
fi

# 2. 分析代码
echo "Analyzing code..."
flutter analyze

if [ $? -ne 0 ]; then
  echo "❌ Code analysis failed. Please fix the issues and try again."
  exit 1
fi

# 3. 运行测试
echo "Running tests..."
flutter test

if [ $? -ne 0 ]; then
  echo "❌ Tests failed. Please fix the failing tests and try again."
  exit 1
fi

# 4. 检查依赖
echo "Checking dependencies..."
flutter pub deps --no-dev

if [ $? -ne 0 ]; then
  echo "❌ Dependency check failed."
  exit 1
fi

echo "✅ All pre-commit checks passed!"
exit 0
```

### 🔧 构建配置

#### 1. 多环境配置

```dart
// lib/core/config/app_config.dart
abstract class AppConfig {
  static AppConfig get instance => _instance;
  static late AppConfig _instance;

  static void setConfig(AppConfig config) {
    _instance = config;
  }

  String get appName;
  String get apiBaseUrl;
  String get websocketUrl;
  bool get enableLogging;
  bool get enableAnalytics;
  Duration get requestTimeout;
  int get maxRetries;
}

// lib/core/config/development_config.dart
class DevelopmentConfig extends AppConfig {
  @override
  String get appName => 'TaskApp (Dev)';

  @override
  String get apiBaseUrl => 'https://dev-api.taskapp.com';

  @override
  String get websocketUrl => 'wss://dev-ws.taskapp.com';

  @override
  bool get enableLogging => true;

  @override
  bool get enableAnalytics => false;

  @override
  Duration get requestTimeout => const Duration(seconds: 30);

  @override
  int get maxRetries => 3;
}

// lib/core/config/production_config.dart
class ProductionConfig extends AppConfig {
  @override
  String get appName => 'TaskApp';

  @override
  String get apiBaseUrl => 'https://api.taskapp.com';

  @override
  String get websocketUrl => 'wss://ws.taskapp.com';

  @override
  bool get enableLogging => false;

  @override
  bool get enableAnalytics => true;

  @override
  Duration get requestTimeout => const Duration(seconds: 10);

  @override
  int get maxRetries => 1;
}

// lib/main_development.dart
void main() {
  AppConfig.setConfig(DevelopmentConfig());
  runApp(const MyApp());
}

// lib/main_production.dart
void main() {
  AppConfig.setConfig(ProductionConfig());
  runApp(const MyApp());
}
```

#### 2. 构建脚本

```bash
#!/bin/bash
# scripts/build.sh

set -e

echo "🚀 Starting build process..."

# 检查参数
if [ -z "$1" ]; then
    echo "Usage: $0 [dev|staging|prod] [platform]"
    echo "Example: $0 prod android"
    exit 1
fi

FLAVOR=$1
PLATFORM=${2:-"all"}

# 清理缓存
echo "🧹 Cleaning cache..."
flutter clean
flutter pub get

# 代码生成
echo "🔨 Generating code..."
flutter packages pub run build_runner build --delete-conflicting-outputs

# 运行测试
echo "🧪 Running tests..."
flutter test --coverage

# 生成覆盖率报告
echo "📊 Generating coverage report..."
genhtml coverage/lcov.info -o coverage/html

# 构建应用
case $PLATFORM in
  android)
    echo "📱 Building Android app for $FLAVOR..."
    flutter build apk --flavor $FLAVOR -t lib/main_${FLAVOR}.dart --release
    ;;
  ios)
    echo "🍎 Building iOS app for $FLAVOR..."
    flutter build ios --flavor $FLAVOR -t lib/main_${FLAVOR}.dart --release --no-codesign
    ;;
  web)
    echo "🌐 Building Web app for $FLAVOR..."
    flutter build web -t lib/main_${FLAVOR}.dart --release
    ;;
  all)
    echo "📱 Building all platforms for $FLAVOR..."
    # Android
    flutter build apk --flavor $FLAVOR -t lib/main_${FLAVOR}.dart --release
    # iOS (如果在macOS上)
    if [[ "$OSTYPE" == "darwin"* ]]; then
      flutter build ios --flavor $FLAVOR -t lib/main_${FLAVOR}.dart --release --no-codesign
    fi
    # Web
    flutter build web -t lib/main_${FLAVOR}.dart --release
    ;;
  *)
    echo "❌ Unknown platform: $PLATFORM"
    exit 1
    ;;
esac

echo "✅ Build completed successfully!"

# 生成构建报告
echo "📄 Generating build report..."
cat > build_report.md << EOF
# Build Report

- **Flavor**: $FLAVOR
- **Platform**: $PLATFORM
- **Build Time**: $(date)
- **Flutter Version**: $(flutter --version | head -n 1)
- **Dart Version**: $(dart --version)

## Test Coverage
![Coverage](coverage/html/index.html)

## Build Artifacts
EOF

if [ "$PLATFORM" == "android" ] || [ "$PLATFORM" == "all" ]; then
  echo "- Android APK: \`build/app/outputs/flutter-apk/app-${FLAVOR}-release.apk\`" >> build_report.md
fi

if [ "$PLATFORM" == "ios" ] || [ "$PLATFORM" == "all" ]; then
  echo "- iOS IPA: \`build/ios/ipa/\`" >> build_report.md
fi

if [ "$PLATFORM" == "web" ] || [ "$PLATFORM" == "all" ]; then
  echo "- Web Build: \`build/web/\`" >> build_report.md
fi

echo "📄 Build report saved to build_report.md"
```

### 📊 CI/CD配置

#### 1. GitHub Actions

```yaml
# .github/workflows/ci.yml
name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.10.0'
        channel: 'stable'

    - name: Get dependencies
      run: flutter pub get

    - name: Generate code
      run: flutter packages pub run build_runner build --delete-conflicting-outputs

    - name: Analyze code
      run: flutter analyze

    - name: Check formatting
      run: dart format lib/ test/ --set-exit-if-changed

    - name: Run tests
      run: flutter test --coverage

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: coverage/lcov.info

  build-android:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v3

    - name: Setup Java
      uses: actions/setup-java@v3
      with:
        distribution: 'zulu'
        java-version: '11'

    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.10.0'
        channel: 'stable'

    - name: Get dependencies
      run: flutter pub get

    - name: Generate code
      run: flutter packages pub run build_runner build --delete-conflicting-outputs

    - name: Build APK
      run: flutter build apk --flavor prod -t lib/main_production.dart --release

    - name: Upload APK
      uses: actions/upload-artifact@v3
      with:
        name: app-prod-release.apk
        path: build/app/outputs/flutter-apk/app-prod-release.apk

  build-ios:
    needs: test
    runs-on: macos-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v3

    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.10.0'
        channel: 'stable'

    - name: Get dependencies
      run: flutter pub get

    - name: Generate code
      run: flutter packages pub run build_runner build --delete-conflicting-outputs

    - name: Build iOS
      run: flutter build ios --flavor prod -t lib/main_production.dart --release --no-codesign

    - name: Create IPA
      run: |
        mkdir Payload
        cp -r build/ios/iphoneos/Runner.app Payload/
        zip -r app-prod-release.ipa Payload/

    - name: Upload IPA
      uses: actions/upload-artifact@v3
      with:
        name: app-prod-release.ipa
        path: app-prod-release.ipa
```

#### 2. 自动化部署

```bash
#!/bin/bash
# scripts/deploy.sh

set -e

FLAVOR=${1:-"prod"}
PLATFORM=${2:-"android"}

echo "🚀 Starting deployment for $FLAVOR on $PLATFORM..."

# 构建应用
./scripts/build.sh $FLAVOR $PLATFORM

case $PLATFORM in
  android)
    echo "📱 Deploying to Google Play..."
    # 使用 fastlane 或其他工具部署到 Google Play
    fastlane android deploy flavor:$FLAVOR
    ;;
  ios)
    echo "🍎 Deploying to App Store..."
    # 使用 fastlane 或其他工具部署到 App Store
    fastlane ios deploy flavor:$FLAVOR
    ;;
  web)
    echo "🌐 Deploying to web server..."
    # 部署到 Firebase Hosting 或其他静态网站托管服务
    firebase deploy --project=$FLAVOR
    ;;
esac

echo "✅ Deployment completed successfully!"
```

### 🎯 学习成长路径

#### 阶段1：基础掌握（1-2个月）

```dart
// 学习目标检查清单
class LearningMilestones {
  // ✅ 基础语法
  static final basicSyntax = [
    '变量声明和类型推断',
    '函数定义和调用',
    '控制流程（if/else, for, while）',
    '集合类型（List, Map, Set）',
    '字符串操作和插值',
  ];

  // ✅ 面向对象
  static final objectOriented = [
    '类和对象的创建',
    '构造函数的多种形式',
    '继承和多态的使用',
    '抽象类和接口',
    'Mixins的概念和应用',
  ];

  // ✅ 核心特性
  static final coreFeatures = [
    '空安全的理解和应用',
    'Future和async/await',
    'Stream的基础使用',
    '泛型的基本概念',
    '扩展方法的创建和使用',
  ];
}
```

#### 阶段2：进阶应用（2-3个月）

```dart
class AdvancedMilestones {
  // ✅ Flutter开发
  static final flutterDevelopment = [
    'Widget生命周期管理',
    '状态管理模式（Provider/BLoC）',
    '导航和路由',
    '网络请求和数据持久化',
    '平台通道的使用',
  ];

  // ✅ 架构设计
  static final architecture = [
    'Clean Architecture的实践',
    '依赖注入的配置',
    '错误处理和异常管理',
    '测试驱动开发',
    '性能优化技巧',
  ];
}
```

#### 阶段3：专家级别（3-6个月）

```dart
class ExpertMilestones {
  // ✅ 高级特性
  static final advancedFeatures = [
    'Isolate并发编程',
    '自定义渲染对象',
    '插件开发',
    '原生集成',
    '代码生成工具',
  ];

  // ✅ 工程实践
  static final engineering = [
    'CI/CD流水线搭建',
    '多环境配置管理',
    '监控和分析集成',
    '安全性最佳实践',
    '团队协作规范',
  ];
}
```


---

## 🎯 学习路径总结

### 初学者路径（0-3个月）


1. ✅ 基础语法和数据类型
2. ✅ 面向对象编程
3. ✅ 异步编程基础
4. ✅ 集合操作

### 中级开发者路径（3-6个月）


1. ✅ 空安全机制
2. ✅ 泛型编程
3. ✅ 扩展方法
4. ✅ Flutter专用特性

### 高级开发者路径（6-12个月）


1. ✅ Isolate并发编程
2. ✅ 架构设计模式
3. ✅ 性能优化
4. ✅ 测试与调试
5. ✅ 完整项目实战

### 专家级路径（1年以上）


1. ✅ 工具链和开发环境
2. ✅ 团队协作和代码规范
3. ✅ 开源贡献
4. ✅ 技术分享和培训

记住：**编程是一门实用技能，最好的学习方式就是动手实践！**


---

*🎉 恭喜你完成了这份全面的Dart学习指南！现在你已经具备了从零基础到专家级别的完整知识体系。继续实践，不断进步！*


1. ✅ 类与对象
2. ✅ 继承与多态
3. ✅ 抽象类与接口
4. ✅ Mixins

### 阶段3：高级特性（2-3周）


1. ✅ 空安全
2. ✅ 异步编程
3. ✅ 泛型
4. ✅ 扩展方法

### 实战建议

**每天练习：**

```dart
// 写一个完整的用户管理系统
// 包含：用户注册、登录、资料修改、权限管理
// 使用：类、异步、空安全、泛型、扩展方法
```

**调试技巧：**

- 使用 `print()` 调试简单问题
- 使用断点调试复杂逻辑
- 阅读错误信息，从下到上看

**学习资源：**

- [Dart官方文档](https://dart.dev/guides)
- [DartPad在线练习](https://dartpad.dev)
- Flutter官方示例代码

记住：**写代码就像学骑自行车，摔几次就会了！**
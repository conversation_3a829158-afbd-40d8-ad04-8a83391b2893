# Flutter Scaffold 快速参考手册

## 📋 概述

本文档是 Flutter Scaffold 项目的快速参考手册，汇总了开发过程中最常用的命令、配置、API和最佳实践，为开发者提供快速查找和使用指导。

## 🚀 常用命令速查

### 项目管理命令
```bash
# 环境设置 (推荐使用make)
make setup                    # 初始化开发环境
mise run setup               # 或使用: mise r s
flutter doctor               # 检查环境配置

# 代码生成
make gen                     # 生成所有代码
mise run gen                 # 或使用: mise r g
dart run build_runner watch # 监听模式生成

# 构建和运行
make run-dev                # 运行开发环境
mise run run-dev            # 或使用: mise r dev
make run-stag               # 运行测试环境
mise run run-stag           # 或使用: mise r stag
make run-prod               # 运行生产环境
mise run run-prod           # 或使用: mise r prod

# 质量检查
make analyze                # 代码分析
mise run analyze            # 或使用: mise r a
make test                   # 运行测试
mise run test               # 或使用: mise r t
make format                 # 代码格式化
mise run format             # 或使用: mise r f
```

### Mason 模板命令
```bash
# 查看可用模板
make mason-list             # 或使用: mason list
mise run mason-list         # 或使用: mise r ml

# 创建完整功能模块 (推荐)
make feature                # 交互式创建功能模块
mise run feature            # 或使用: mise r feat

# 创建单独组件
make page                   # 交互式创建页面
mise run page               # 或使用: mise r pg
make widget                 # 交互式创建组件
mise run widget             # 或使用: mise r wg
make validator              # 交互式创建验证器
mise run validator          # 或使用: mise r val
```

### 构建命令
```bash
# Android APK构建 (推荐使用make)
make build-dev              # 开发环境APK
mise run build-dev          # 或使用: mise r bd
make build-stag             # 测试环境APK
mise run build-stag         # 或使用: mise r bs-stag
make build-prod             # 生产环境APK
mise run build-prod         # 或使用: mise r bp

# iOS构建
make build-ios-dev          # 开发环境iOS
mise run build-ios-dev      # 或使用: mise r bid
make build-ios-stag         # 测试环境iOS
mise run build-ios-stag     # 或使用: mise r bis
make build-ios-prod         # 生产环境iOS
mise run build-ios-prod     # 或使用: mise r bip

# 其他平台构建 (直接使用flutter命令)
flutter build web --release                    # Web发布构建
flutter build appbundle --release              # Android AAB (推荐)
flutter build windows --release                # Windows
flutter build macos --release                  # macOS
flutter build linux --release                  # Linux
```

## 🏗️ 架构模式速查

### Clean Architecture 层级
```
Presentation Layer (UI)
├── Pages (路由页面)
├── Widgets (UI组件)
└── BLoC (状态管理)
    ├── Events (事件)
    ├── States (状态)
    └── Blocs (业务逻辑)

Domain Layer (业务逻辑)
├── Entities (实体)
├── UseCases (用例)
├── Repositories (仓库接口)
└── Failures (失败类型)

Data Layer (数据)
├── Models (数据模型)
├── DataSources (数据源)
├── Repositories (仓库实现)
└── Mappers (数据映射)
```

### 目录结构模板
```
lib/features/[feature_name]/
├── data/
│   ├── datasources/
│   │   ├── [feature]_local_datasource.dart
│   │   └── [feature]_remote_datasource.dart
│   ├── models/
│   │   └── [feature]_model.dart
│   └── repositories/
│       └── [feature]_repository_impl.dart
├── domain/
│   ├── entities/
│   │   └── [feature].dart
│   ├── repositories/
│   │   └── [feature]_repository.dart
│   └── usecases/
│       ├── get_[feature].dart
│       ├── create_[feature].dart
│       └── update_[feature].dart
└── presentation/
    ├── bloc/
    │   ├── [feature]_bloc.dart
    │   ├── [feature]_event.dart
    │   └── [feature]_state.dart
    ├── pages/
    │   └── [feature]_page.dart
    └── widgets/
        └── [feature]_widget.dart
```

## 🎨 主题系统速查

### 颜色使用
```dart
// 标准颜色
context.colors.primary              // 主色
context.colors.secondary            // 次要色
context.colors.surface              // 表面色
context.colors.background           // 背景色
context.colors.error                // 错误色

// 自定义颜色
context.customColors.success        // 成功色
context.customColors.warning        // 警告色
context.customColors.info           // 信息色

// 透明度变体 (高性能)
context.colors.primary.light        // 10% 透明度
context.colors.primary.medium       // 30% 透明度
context.colors.primary.strong       // 70% 透明度
context.colors.primary.intense      // 80% 透明度
context.colors.primary.disabled     // 38% 透明度
```

### 文本样式
```dart
// 标准文本样式
context.textStyles.displayLarge     // 57sp
context.textStyles.displayMedium    // 45sp
context.textStyles.displaySmall     // 36sp
context.textStyles.headlineLarge    // 32sp
context.textStyles.headlineMedium   // 28sp
context.textStyles.headlineSmall    // 24sp
context.textStyles.titleLarge       // 22sp
context.textStyles.titleMedium      // 16sp
context.textStyles.titleSmall       // 14sp
context.textStyles.bodyLarge        // 16sp
context.textStyles.bodyMedium       // 14sp
context.textStyles.bodySmall        // 12sp
context.textStyles.labelLarge       // 14sp
context.textStyles.labelMedium      // 12sp
context.textStyles.labelSmall       // 11sp
```

### 响应式设计
```dart
// 断点检查
context.isMobile                    // < 768px
context.isTablet                    // 768px - 1200px
context.isDesktop                   // >= 1200px

// 响应式组件
ResponsiveBuilder(
  mobile: (context, constraints) => MobileWidget(),
  tablet: (context, constraints) => TabletWidget(),
  desktop: (context, constraints) => DesktopWidget(),
)

// 间距系统
context.spacing.xs                  // 4px
context.spacing.sm                  // 8px
context.spacing.md                  // 16px
context.spacing.lg                  // 24px
context.spacing.xl                  // 32px
context.spacing.xxl                 // 48px
```

## 🔧 状态管理速查

### BLoC 模式
```dart
// Event 定义
abstract class AuthEvent extends Equatable {
  const AuthEvent();
  @override
  List<Object> get props => [];
}

class LoginRequested extends AuthEvent {
  final String email;
  final String password;

  const LoginRequested({required this.email, required this.password});

  @override
  List<Object> get props => [email, password];
}

// State 定义
abstract class AuthState extends Equatable {
  const AuthState();
  @override
  List<Object?> get props => [];
}

class AuthInitial extends AuthState {}
class AuthLoading extends AuthState {}
class AuthSuccess extends AuthState {
  final User user;
  const AuthSuccess(this.user);
  @override
  List<Object> get props => [user];
}
class AuthFailure extends AuthState {
  final String message;
  const AuthFailure(this.message);
  @override
  List<Object> get props => [message];
}

// BLoC 实现
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  AuthBloc() : super(AuthInitial()) {
    on<LoginRequested>(_onLoginRequested);
  }

  Future<void> _onLoginRequested(
    LoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());
    try {
      final user = await _authRepository.login(event.email, event.password);
      emit(AuthSuccess(user));
    } catch (e) {
      emit(AuthFailure(e.toString()));
    }
  }
}

// UI 使用
BlocBuilder<AuthBloc, AuthState>(
  builder: (context, state) {
    if (state is AuthLoading) {
      return CircularProgressIndicator();
    } else if (state is AuthSuccess) {
      return WelcomeWidget(user: state.user);
    } else if (state is AuthFailure) {
      return ErrorWidget(message: state.message);
    }
    return LoginForm();
  },
)
```

## 🌐 网络层速查

### API 客户端配置
```dart
// Dio 配置
final dio = Dio(BaseOptions(
  baseUrl: 'https://api.example.com',
  connectTimeout: Duration(seconds: 30),
  receiveTimeout: Duration(seconds: 30),
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
));

// 拦截器添加
dio.interceptors.addAll([
  TokenInterceptor(),           // Token 管理
  SmartCacheInterceptor(),      // 智能缓存
  LoggingInterceptor(),         // 日志记录
  ErrorInterceptor(),           // 错误处理
]);

// Retrofit API 定义
@RestApi()
abstract class ApiClient {
  factory ApiClient(Dio dio) = _ApiClient;

  @GET('/users/{id}')
  Future<User> getUser(@Path('id') String id);

  @POST('/users')
  Future<User> createUser(@Body() CreateUserRequest request);

  @PUT('/users/{id}')
  Future<User> updateUser(@Path('id') String id, @Body() UpdateUserRequest request);

  @DELETE('/users/{id}')
  Future<void> deleteUser(@Path('id') String id);
}
```

### 错误处理
```dart
// 统一错误处理
try {
  final result = await apiClient.getUser('123');
  return Right(result);
} on DioException catch (e) {
  return Left(NetworkFailure.fromDioException(e));
} catch (e) {
  return Left(UnknownFailure(message: e.toString()));
}

// 错误类型
abstract class Failure extends Equatable {
  final String message;
  const Failure({required this.message});

  @override
  List<Object> get props => [message];
}

class NetworkFailure extends Failure {
  final int? statusCode;
  final String? code;

  const NetworkFailure({
    required String message,
    this.statusCode,
    this.code,
  }) : super(message: message);

  factory NetworkFailure.fromDioException(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
        return NetworkFailure(message: '连接超时', code: 'CONNECTION_TIMEOUT');
      case DioExceptionType.sendTimeout:
        return NetworkFailure(message: '发送超时', code: 'SEND_TIMEOUT');
      case DioExceptionType.receiveTimeout:
        return NetworkFailure(message: '接收超时', code: 'RECEIVE_TIMEOUT');
      case DioExceptionType.badResponse:
        return NetworkFailure(
          message: '服务器错误',
          statusCode: e.response?.statusCode,
          code: 'BAD_RESPONSE',
        );
      default:
        return NetworkFailure(message: '网络错误', code: 'UNKNOWN');
    }
  }
}
```

## 🔒 安全配置速查

### 安全存储
```dart
// 安全存储服务
@lazySingleton
class SecureStorageService {
  static const _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
      sharedPreferencesName: 'flutter_scaffold_secure_prefs',
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  // 存储认证数据
  Future<void> storeAuthData({
    required String accessToken,
    required String refreshToken,
    required String tokenExpiry,
    required String userId,
  }) async {
    await Future.wait([
      _storage.write(key: 'access_token', value: accessToken),
      _storage.write(key: 'refresh_token', value: refreshToken),
      _storage.write(key: 'token_expiry', value: tokenExpiry),
      _storage.write(key: 'user_id', value: userId),
    ]);
  }

  // 获取访问令牌
  Future<String?> getAccessToken() async {
    return await _storage.read(key: 'access_token');
  }

  // 清除所有数据
  Future<void> clearAll() async {
    await _storage.deleteAll();
  }
}
```

### 输入验证
```dart
// 验证器
class Validators {
  static String? email(String? value) {
    if (value == null || value.isEmpty) {
      return '邮箱不能为空';
    }
    if (!RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(value)) {
      return '请输入有效的邮箱地址';
    }
    return null;
  }

  static String? password(String? value) {
    if (value == null || value.isEmpty) {
      return '密码不能为空';
    }
    if (value.length < 8) {
      return '密码至少需要8位字符';
    }
    if (!RegExp(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]').hasMatch(value)) {
      return '密码必须包含大小写字母、数字和特殊字符';
    }
    return null;
  }

  static String? required(String? value, [String? fieldName]) {
    if (value == null || value.trim().isEmpty) {
      return '${fieldName ?? '此字段'}不能为空';
    }
    return null;
  }
}
```

## 📦 依赖注入速查

### GetIt + Injectable 配置
```dart
// 依赖注入配置
@InjectableInit()
void configureDependencies() => getIt.init();

// 服务注册
@lazySingleton
class UserRepository {
  final ApiClient _apiClient;
  final LocalDataSource _localDataSource;

  UserRepository(this._apiClient, this._localDataSource);
}

// 工厂注册
@injectable
class UserBloc {
  final UserRepository _repository;

  UserBloc(this._repository);
}

// 单例注册
@singleton
class AppConfig {
  final String apiBaseUrl;
  final bool enableLogging;

  AppConfig({required this.apiBaseUrl, required this.enableLogging});
}

// 使用依赖
final userRepository = getIt<UserRepository>();
final userBloc = getIt<UserBloc>();
```

### 环境配置
```dart
// 多环境配置
@Environment('dev')
@lazySingleton
class DevApiClient implements ApiClient {
  // 开发环境实现
}

@Environment('prod')
@lazySingleton
class ProdApiClient implements ApiClient {
  // 生产环境实现
}

// 初始化时指定环境
void configureDependencies() => getIt.init(environment: Environment.dev);
```

## 🧪 测试速查

### 单元测试
```dart
// 测试文件结构
test/
├── unit/
│   ├── domain/
│   │   ├── entities/
│   │   └── usecases/
│   ├── data/
│   │   ├── models/
│   │   └── repositories/
│   └── presentation/
│       └── bloc/
├── widget/
└── integration/

// BLoC 测试示例
void main() {
  group('AuthBloc', () {
    late AuthBloc authBloc;
    late MockAuthRepository mockAuthRepository;

    setUp(() {
      mockAuthRepository = MockAuthRepository();
      authBloc = AuthBloc(authRepository: mockAuthRepository);
    });

    tearDown(() {
      authBloc.close();
    });

    blocTest<AuthBloc, AuthState>(
      'emits [AuthLoading, AuthSuccess] when login is successful',
      build: () {
        when(() => mockAuthRepository.login(any(), any()))
            .thenAnswer((_) async => User(id: '1', name: 'Test User'));
        return authBloc;
      },
      act: (bloc) => bloc.add(LoginRequested(email: '<EMAIL>', password: 'password')),
      expect: () => [
        AuthLoading(),
        AuthSuccess(User(id: '1', name: 'Test User')),
      ],
    );
  });
}
```

### Widget 测试
```dart
void main() {
  group('LoginPage', () {
    testWidgets('should display login form', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider(
            create: (_) => MockAuthBloc(),
            child: LoginPage(),
          ),
        ),
      );

      expect(find.byType(TextFormField), findsNWidgets(2));
      expect(find.byType(ElevatedButton), findsOneWidget);
      expect(find.text('登录'), findsOneWidget);
    });

    testWidgets('should trigger login when button is pressed', (tester) async {
      final mockBloc = MockAuthBloc();

      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider.value(
            value: mockBloc,
            child: LoginPage(),
          ),
        ),
      );

      await tester.enterText(find.byKey(Key('email_field')), '<EMAIL>');
      await tester.enterText(find.byKey(Key('password_field')), 'password');
      await tester.tap(find.byKey(Key('login_button')));

      verify(() => mockBloc.add(LoginRequested(
        email: '<EMAIL>',
        password: 'password',
      ))).called(1);
    });
  });
}
```

## 🔧 故障排除速查

### 常见问题解决
```bash
# 依赖冲突 (推荐使用make)
make clean                  # 清理所有包
mise run clean              # 或使用: mise r c
make bootstrap              # 重新安装依赖
mise run bootstrap          # 或使用: mise r bs

# 代码生成问题
make gen                    # 重新生成代码
mise run gen                # 或使用: mise r g
# 或手动清理后生成:
find . -name "*.g.dart" -delete
find . -name "*.freezed.dart" -delete
dart run build_runner build --delete-conflicting-outputs

# Android 构建失败
make build-dev              # 使用make构建
mise run build-dev          # 或使用: mise r bd
# 或手动清理:
cd android && ./gradlew clean && cd ..

# iOS 构建失败
make build-ios-dev          # 使用make构建
mise run build-ios-dev      # 或使用: mise r bid
# 或手动清理:
cd ios && rm -rf build/ Pods/ Podfile.lock && pod install && cd ..

# 环境检查
flutter doctor             # 检查Flutter环境
mise run flutter-doctor     # 或使用: mise r doctor
```

### 性能问题诊断
```dart
// 性能监控
import 'package:flutter/rendering.dart';

void main() {
  // 启用性能叠加层
  debugPaintSizeEnabled = false;
  debugRepaintRainbowEnabled = false;

  runApp(MyApp());
}

// 内存泄漏检测
class MemoryLeakDetector {
  static void trackWidget(String name) {
    print('Widget created: $name');
  }

  static void trackDispose(String name) {
    print('Widget disposed: $name');
  }
}
```

## 📱 平台特定配置速查

### Android 配置
```gradle
// android/app/build.gradle
android {
    compileSdkVersion 34
    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 34
    }

    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    flavorDimensions "environment"
    productFlavors {
        development {
            dimension "environment"
            applicationIdSuffix ".dev"
        }
        production {
            dimension "environment"
        }
    }
}
```

### iOS 配置
```xml
<!-- ios/Runner/Info.plist -->
<key>NSAppTransportSecurity</key>
<dict>
    <key>NSAllowsArbitraryLoads</key>
    <false/>
    <key>NSExceptionDomains</key>
    <dict>
        <key>api.example.com</key>
        <dict>
            <key>NSExceptionAllowsInsecureHTTPLoads</key>
            <false/>
        </dict>
    </dict>
</dict>
```

### Web 配置
```html
<!-- web/index.html -->
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flutter Scaffold</title>
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#2196F3">
</head>
```

## 🎯 最佳实践速查

### 代码规范
```dart
// 命名规范
class UserRepository {}           // PascalCase for classes
final userRepository = ...;      // camelCase for variables
const API_BASE_URL = ...;        // SCREAMING_SNAKE_CASE for constants

// 文件命名
user_repository.dart             // snake_case for files
user_repository_test.dart        // test files with _test suffix

// 目录命名
lib/features/user_management/    // snake_case for directories
```

### 性能优化
```dart
// 使用 const 构造函数
const Text('Hello World');

// 避免不必要的重建
class MyWidget extends StatelessWidget {
  const MyWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MyBloc, MyState>(
      buildWhen: (previous, current) => previous.data != current.data,
      builder: (context, state) => Text(state.data),
    );
  }
}

// 使用 ListView.builder 处理大列表
ListView.builder(
  itemCount: items.length,
  itemBuilder: (context, index) => ListTile(title: Text(items[index])),
)
```

### 错误处理
```dart
// 统一错误处理
Result<T> handleError<T>(Future<T> Function() operation) async {
  try {
    final result = await operation();
    return Success(result);
  } on DioException catch (e) {
    return Failure(NetworkFailure.fromDioException(e));
  } catch (e) {
    return Failure(UnknownFailure(message: e.toString()));
  }
}

// 使用 Either 类型
Either<Failure, User> result = await userRepository.getUser('123');
result.fold(
  (failure) => print('Error: ${failure.message}'),
  (user) => print('Success: ${user.name}'),
);
```

## 📚 相关文档链接

### 核心技术文档
- [Clean Architecture 指南](CLEAN_ARCHITECTURE_GUIDE_PRO.md) - 架构设计详细指南
- [状态管理指南](STATE_MANAGEMENT_GUIDE.md) - BLoC 状态管理完整教程
- [UI 设计指南](UI_GUIDE_PRO.md) - UI 组件和设计系统
- [后端 API 指南](BACKEND_API_GUIDE.md) - API 设计和集成
- [Dart 开发指南](DART_GUIDE_PRO.md) - Dart 语言深度指南

### 工作流和配置
- [开发工作流指南](DEVELOPMENT_WORKFLOW_GUIDE.md) - 完整开发流程
- [工具配置指南](TOOLS_CONFIGURATION_GUIDE.md) - 工具和配置管理

### 项目管理
- [项目总结报告](FINAL_SUMMARY_REPORT.md) - 项目状态和改进建议
- [架构决策记录](adr/) - 重要技术决策记录

## 🔍 快速搜索索引

### 按功能分类
- **状态管理**: BLoC, Cubit, Events, States
- **网络请求**: Dio, Retrofit, 拦截器, 错误处理
- **依赖注入**: GetIt, Injectable, 环境配置
- **主题系统**: 颜色, 文本样式, 响应式设计
- **安全**: 安全存储, 输入验证, 网络安全
- **测试**: 单元测试, Widget测试, 集成测试
- **构建**: Android, iOS, Web, Desktop

### 按使用场景分类
- **新手入门**: 环境设置, 项目结构, 基础命令
- **日常开发**: 代码生成, 调试, 测试运行
- **问题解决**: 故障排除, 性能优化, 错误诊断
- **部署发布**: 构建配置, 平台特定设置, CI/CD

---

**快速参考手册版本**: v1.0
**最后更新**: 2025年1月
**适用项目版本**: Flutter Scaffold v1.0+
```

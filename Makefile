# Flutter Scaffold - Makefile
# Quick commands for development workflow

.PHONY: setup bootstrap clean test build run dev

# Environment variables
FLUTTER_VERSION ?= 3.32.5
APP_DIR := apps/mobile

# Setup development environment
setup:
	@echo "Setting up Flutter Scaffold development environment..."
	@echo "Installing Melos..."
	dart pub global activate melos
	@echo "Installing Mason..."
	dart pub global activate mason_cli
	@echo "Installing Patrol..."
	dart pub global activate patrol_cli
	@echo "Bootstrap packages..."
	melos bootstrap
	@echo "Setup complete! 🚀"

# Bootstrap all packages
bootstrap:
	melos bootstrap

# Clean all packages
clean:
	melos run clean

# Run tests for all packages
test:
	melos run test

# Run tests with coverage
test-coverage:
	melos run test --coverage

# Generate code for all packages
gen:
	melos run gen

# Format all packages
format:
	melos run format

# Analyze all packages
analyze:
	melos run analyze

# Run development server
run-dev:
	cd $(APP_DIR) && flutter run --flavor development -t lib/main_development.dart

# Run staging server
run-stag:
	cd $(APP_DIR) && flutter run --flavor staging -t lib/main_staging.dart

# Run production server
run-prod:
	cd $(APP_DIR) && flutter run --flavor production -t lib/main_production.dart

# Build APK for development
build-dev:
	cd $(APP_DIR) && flutter build apk --flavor development -t lib/main_development.dart

# Build APK for staging
build-stag:
	cd $(APP_DIR) && flutter build apk --flavor staging -t lib/main_staging.dart

# Build APK for production
build-prod:
	cd $(APP_DIR) && flutter build apk --flavor production -t lib/main_production.dart

# Build iOS for development
build-ios-dev:
	cd $(APP_DIR) && flutter build ios --flavor development -t lib/main_development.dart --no-codesign

# Build iOS for staging
build-ios-stag:
	cd $(APP_DIR) && flutter build ios --flavor staging -t lib/main_staging.dart --no-codesign

# Build iOS for production
build-ios-prod:
	cd $(APP_DIR) && flutter build ios --flavor production -t lib/main_production.dart --no-codesign

# Mason Templates - Code Generation (Streamlined)
# Generate complete feature with modern architecture
feature:
	@echo "🧱 Creating modern feature..."
	@echo "This will create a complete feature following Clean Architecture."
	@echo "Includes: Entity, Repository, UseCase, BLoC, DataSource, Model, and Page."
	@echo "Follow the interactive prompts to configure your feature."
	mason make feature

# Generate page with theme integration
page:
	@echo "🧱 Creating page..."
	@echo "This will create a page widget with modern theme integration and optional BLoC support."
	@echo "Follow the interactive prompts to configure your page."
	mason make page

# Generate reusable widget component
widget:
	@echo "🧱 Creating widget..."
	@echo "This will create a reusable widget component with theme integration."
	@echo "Follow the interactive prompts to configure your widget."
	mason make widget



# Generate input validator
validator:
	@echo "🧱 Creating validator..."
	@echo "This will create a comprehensive input validator with business rules."
	@echo "Follow the interactive prompts to configure your validator."
	mason make validator

# Generate new ADR
adr:
	@echo "📝 Creating Architecture Decision Record..."
	@echo "This will create a new ADR document for recording technical decisions."
	@echo "Follow the interactive prompts to configure your ADR."
	mason make adr

# List all available Mason templates
mason-list:
	@echo "🧱 Available Mason templates:"
	@mason list

# Get Mason template info
mason-info:
	@read -p "Enter template name: " template; \
	mason info $$template

# Update dependencies
upgrade:
	melos run upgrade

# Check for outdated packages
outdated:
	melos exec -- flutter pub outdated

# Run integration tests
integration:
	cd $(APP_DIR) && patrol test integration_test

# Help
help:
	@echo "🚀 Flutter Scaffold - Available Commands:"
	@echo ""
	@echo "📦 Environment & Dependencies:"
	@echo "  setup           - Setup development environment"
	@echo "  bootstrap       - Bootstrap all packages"
	@echo "  clean           - Clean all packages"
	@echo "  upgrade         - Update dependencies"
	@echo "  outdated        - Check for outdated packages"
	@echo ""
	@echo "🧪 Testing & Quality:"
	@echo "  test            - Run tests for all packages"
	@echo "  test-coverage   - Run tests with coverage"
	@echo "  integration     - Run integration tests"
	@echo "  analyze         - Analyze all packages"
	@echo "  format          - Format all packages"
	@echo ""
	@echo "🏗️  Build & Run:"
	@echo "  run-dev         - Run development server"
	@echo "  run-stag        - Run staging server"
	@echo "  run-prod        - Run production server"
	@echo "  build-dev       - Build APK for development"
	@echo "  build-stag      - Build APK for staging"
	@echo "  build-prod      - Build APK for production"
	@echo "  build-ios-dev   - Build iOS for development"
	@echo "  build-ios-stag  - Build iOS for staging"
	@echo "  build-ios-prod  - Build iOS for production"
	@echo ""
	@echo "🧱 Code Generation (Mason Templates - Streamlined):"
	@echo "  feature         - Generate complete feature with Clean Architecture (interactive)"
	@echo "  page            - Generate page with theme integration and BLoC support (interactive)"
	@echo "  widget          - Generate reusable widget component (interactive)"
	@echo "  validator       - Generate comprehensive input validator (interactive)"
	@echo "  adr             - Generate Architecture Decision Record (interactive)"
	@echo "  mason-list      - List all available Mason templates"
	@echo "  mason-info      - Get Mason template information"
	@echo ""
	@echo "🔧 Other:"
	@echo "  gen             - Generate code for all packages"
name: validator
description: Creates a comprehensive input validator with business rules and internationalization
version: 1.0.0

vars:
  name:
    type: string
    description: Validator name (e.g., user_input, form_data, email)
    default: example
    prompt: What is the name of the validator?

  feature:
    type: string
    description: Feature name this validator belongs to
    default: core
    prompt: What feature does this validator belong to?
name: page
description: Creates a modern page with theme integration and optional BLoC support
version: 1.0.0

vars:
  name:
    type: string
    description: Page name (e.g., user_profile, settings)
    default: example
    prompt: What is the name of the page?

  feature:
    type: string
    description: Feature name this page belongs to
    default: core
    prompt: What feature does this page belong to?

  bloc:
    type: boolean
    description: Include BLoC state management integration
    default: true
    prompt: Do you want to include BLoC integration?

  entity:
    type: string
    description: Entity name for BLoC operations (e.g., User, Product)
    default: ""
    prompt: What entity does this page work with? (if using BLoC)
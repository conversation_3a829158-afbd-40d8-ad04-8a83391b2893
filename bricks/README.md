# Mason Bricks Templates (Streamlined)

This directory contains streamlined Mason brick templates for modern Flutter development.

## Important Notes

⚠️ **Template Files**: Files in `__brick__` directories are Mason templates, not regular Dart files. They contain template syntax like `{{name.snakeCase()}}` which will be replaced during code generation.

## Available Bricks (5 Core Templates)

### feature
Generates complete Clean Architecture feature with modern tech stack.

**Usage:**
```bash
mason make feature
```

**Generated files:**
- Complete data, domain, and presentation layers
- Retrofit + Injectable RemoteDataSource (API客户端)
- Freezed + JsonSerializable Model
- Injectable Repository and UseCase
- Freezed BLoC with sealed states
- Theme-integrated Page

### page
Generates modern page with theme integration and optional BLoC support.

**Usage:**
```bash
mason make page
```

**Generated files:**
- `lib/features/{feature}/presentation/pages/{name}_page.dart`

### widget
Generates reusable widget component with theme integration.

**Usage:**
```bash
mason make widget
```

**Generated files:**
- `lib/features/{feature}/presentation/widgets/{name}_widget.dart`



### validator
Generates comprehensive input validator with business rules.

**Usage:**
```bash
mason make validator
```

**Generated files:**
- `lib/features/{feature}/domain/validators/{name}_validator.dart`

### adr
Generates Architecture Decision Record for technical decisions.

**Usage:**
```bash
mason make adr
```

**Generated files:**
- `docs/adr/{number}-{name}.md`

## Development Guidelines

1. **Do not edit generated files directly** - they will be overwritten
2. **Template syntax** uses double curly braces: `{{variable}}`
3. **Dependencies** are resolved in the target project, not in the brick template
4. **IDE warnings** in `__brick__` directories are expected and should be ignored

## Code Generation Workflow

1. Create/modify brick template in `__brick__` directory
2. Run `mason make <brick_name>` to generate code
3. Run `dart run build_runner build` to generate additional files (*.g.dart)

## Troubleshooting

If you see Dart analysis errors in brick templates:
- These are expected and can be safely ignored
- The errors occur because templates contain Mason syntax, not valid Dart
- Generated code will be valid Dart after template processing
